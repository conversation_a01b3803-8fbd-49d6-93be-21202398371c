"""
系统设置相关API路由
"""

import os
import sys
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from module.config import Config
from module.app import Application
from ..auth import get_current_active_user, User
from ..database import get_db
from ..utils import create_response

router = APIRouter()

# 全局配置实例
config_manager = Config()
app_instance = None


def get_app_instance():
    """获取Application实例"""
    global app_instance
    if app_instance is None:
        app_instance = Application(
            config_file="config.yaml",
            app_data_file="app_data.yaml",
            application_name="TelegramMediaDownloader"
        )
        app_instance.load_config()
    return app_instance


class TelegramSettings(BaseModel):
    """Telegram配置"""
    apiId: int = Field(default=0, description="Telegram API ID")
    apiHash: str = Field(default="", description="Telegram API Hash")
    sessionName: str = Field(default="telegram_session", description="会话名称")
    botToken: Optional[str] = Field(default=None, description="Bot Token")
    sessionPath: str = Field(default="./sessions", description="会话文件路径")


class FileSettings(BaseModel):
    """文件处理配置"""
    maxFileSize: int = Field(default=2*1024*1024*1024, description="最大文件大小(bytes)")
    chunkSize: int = Field(default=1024*1024, description="分块大小(bytes)")
    filePathPrefix: List[str] = Field(default=["chat_title", "media_datetime"], description="文件路径前缀")
    hideFileName: bool = Field(default=False, description="隐藏文件名")
    fileNamePrefix: List[str] = Field(default=["message_id", "file_name"], description="文件名前缀")
    mediaTypes: List[str] = Field(default=["audio", "document", "photo", "video", "voice", "animation"], description="支持的媒体类型")
    fileFormats: Dict[str, List[str]] = Field(default={
        "audio": ["all"],
        "document": ["pdf", "epub"],
        "video": ["mp4"],
        "photo": ["all"],
        "voice": ["all"],
        "animation": ["all"]
    }, description="文件格式过滤")
    enableDownloadTxt: bool = Field(default=False, description="启用下载文本")
    dropNoAudioVideo: bool = Field(default=False, description="丢弃无音视频文件")


class PerformanceSettings(BaseModel):
    """性能配置"""
    timeout: int = Field(default=30, description="超时时间(秒)")
    retryAttempts: int = Field(default=3, description="重试次数")
    retryDelay: int = Field(default=5, description="重试延迟(秒)")
    maxDownloadSpeed: int = Field(default=0, description="最大下载速度(KB/s, 0=无限制)")
    maxConcurrentTransmissions: int = Field(default=1, description="最大并发传输数")
    startTimeout: int = Field(default=60, description="启动超时(秒)")


class SecuritySettings(BaseModel):
    """安全配置"""
    sessionTimeout: int = Field(default=3600, description="会话超时(秒)")
    maxLoginAttempts: int = Field(default=5, description="最大登录尝试次数")
    loginAttemptTimeout: int = Field(default=300, description="登录尝试超时(秒)")
    allowedHosts: List[str] = Field(default=["localhost", "127.0.0.1"], description="允许的主机")
    webLoginSecret: str = Field(default="", description="Web登录密钥")
    allowedUserIds: List[str] = Field(default=[], description="允许的用户ID")


class DatabaseSettings(BaseModel):
    """数据库配置"""
    url: str = Field(default="sqlite:///app.db", description="数据库URL")
    echo: bool = Field(default=False, description="SQL回显")
    poolSize: int = Field(default=10, description="连接池大小")
    maxOverflow: int = Field(default=20, description="最大溢出连接数")


class ProxySettings(BaseModel):
    """代理配置"""
    enabled: bool = Field(default=False, description="启用代理")
    type: str = Field(default="http", description="代理类型")
    host: str = Field(default="", description="代理主机")
    port: int = Field(default=0, description="代理端口")
    username: Optional[str] = Field(default="", description="代理用户名")
    password: Optional[str] = Field(default="", description="代理密码")


class NotificationSettings(BaseModel):
    """通知配置"""
    enabled: bool = Field(default=True, description="启用通知")
    taskCompletion: bool = Field(default=True, description="任务完成通知")
    taskFailure: bool = Field(default=True, description="任务失败通知")
    systemAlerts: bool = Field(default=True, description="系统警告通知")
    emailEnabled: bool = Field(default=False, description="启用邮件通知")
    emailSmtpServer: str = Field(default="", description="SMTP服务器")
    emailSmtpPort: int = Field(default=587, description="SMTP端口")
    emailUsername: str = Field(default="", description="邮件用户名")
    emailPassword: str = Field(default="", description="邮件密码")
    emailFrom: str = Field(default="", description="发件人邮箱")
    emailTo: List[str] = Field(default=[], description="收件人邮箱列表")


class SystemSettings(BaseModel):
    """系统设置模型"""
    downloadPath: str = Field(default="./downloads", description="下载路径")
    maxConcurrentTasks: int = Field(default=3, description="最大并发任务数")
    theme: str = Field(default="light", description="界面主题")
    language: str = Field(default="zh-CN", description="界面语言")
    telegramSettings: TelegramSettings = Field(default_factory=TelegramSettings, description="Telegram配置")
    fileSettings: FileSettings = Field(default_factory=FileSettings, description="文件处理配置")
    performanceSettings: PerformanceSettings = Field(default_factory=PerformanceSettings, description="性能配置")
    securitySettings: SecuritySettings = Field(default_factory=SecuritySettings, description="安全配置")
    databaseSettings: DatabaseSettings = Field(default_factory=DatabaseSettings, description="数据库配置")
    proxySettings: ProxySettings = Field(default_factory=ProxySettings, description="代理配置")
    notificationSettings: NotificationSettings = Field(default_factory=NotificationSettings, description="通知配置")


class SettingsResponse(BaseModel):
    """设置响应模型"""
    success: bool
    data: SystemSettings
    message: str = ""
    timestamp: str


class SettingsUpdateRequest(BaseModel):
    """设置更新请求模型"""
    downloadPath: Optional[str] = None
    maxConcurrentTasks: Optional[int] = None
    theme: Optional[str] = None
    language: Optional[str] = None
    telegramSettings: Optional[TelegramSettings] = None
    fileSettings: Optional[FileSettings] = None
    performanceSettings: Optional[PerformanceSettings] = None
    securitySettings: Optional[SecuritySettings] = None
    databaseSettings: Optional[DatabaseSettings] = None
    proxySettings: Optional[ProxySettings] = None
    notificationSettings: Optional[NotificationSettings] = None


def _convert_app_to_system_settings(app: Application, config: Config) -> SystemSettings:
    """将Application和Config实例转换为SystemSettings模型"""
    return SystemSettings(
        downloadPath=app.save_path,
        maxConcurrentTasks=app.max_download_task,
        theme="light",  # 默认主题，可以从配置中读取
        language=app.language.name.lower().replace('_', '-'),
        telegramSettings=TelegramSettings(
            apiId=int(app.api_id) if app.api_id and app.api_id.isdigit() else 0,
            apiHash=app.api_hash,
            sessionName="telegram_session",
            botToken=app.bot_token if hasattr(app, 'bot_token') else None,
            sessionPath=app.session_file_path
        ),
        fileSettings=FileSettings(
            maxFileSize=2*1024*1024*1024,  # 默认2GB
            chunkSize=1024*1024,  # 默认1MB
            filePathPrefix=app.file_path_prefix,
            hideFileName=app.hide_file_name,
            fileNamePrefix=app.file_name_prefix,
            mediaTypes=app.media_types,
            fileFormats=app.file_formats,
            enableDownloadTxt=app.enable_download_txt,
            dropNoAudioVideo=app.drop_no_audio_video
        ),
        performanceSettings=PerformanceSettings(
            timeout=config.performance.timeout,
            retryAttempts=config.performance.retry_attempts,
            retryDelay=config.performance.retry_delay,
            maxDownloadSpeed=config.performance.max_download_speed,
            maxConcurrentTransmissions=app.max_concurrent_transmissions,
            startTimeout=app.start_timeout
        ),
        securitySettings=SecuritySettings(
            sessionTimeout=config.security.session_timeout,
            maxLoginAttempts=config.security.max_login_attempts,
            loginAttemptTimeout=config.security.login_attempt_timeout,
            allowedHosts=config.security.allowed_hosts,
            webLoginSecret=app.web_login_secret,
            allowedUserIds=[str(uid) for uid in app.allowed_user_ids]
        ),
        databaseSettings=DatabaseSettings(
            url=config.database.url,
            echo=config.database.echo,
            poolSize=config.database.pool_size,
            maxOverflow=config.database.max_overflow
        ),
        proxySettings=ProxySettings(
            enabled=config.proxy.enabled,
            type=config.proxy.scheme,
            host=config.proxy.hostname,
            port=config.proxy.port,
            username=config.proxy.username,
            password=config.proxy.password
        ),
        notificationSettings=NotificationSettings(
            enabled=config.notification.enabled,
            taskCompletion=config.notification.task_completion,
            taskFailure=config.notification.task_failure,
            systemAlerts=config.notification.system_alerts,
            emailEnabled=config.notification.email_enabled,
            emailSmtpServer=config.notification.email_smtp_server,
            emailSmtpPort=config.notification.email_smtp_port,
            emailUsername=config.notification.email_username,
            emailPassword=config.notification.email_password,
            emailFrom=config.notification.email_from,
            emailTo=config.notification.email_to
        )
    )


def _update_app_from_system_settings(
    app: Application,
    config: Config,
    settings: SystemSettings
) -> None:
    """从SystemSettings更新Application和Config实例"""
    # 更新Application实例
    app.save_path = settings.downloadPath
    app.max_download_task = settings.maxConcurrentTasks

    # 更新Telegram设置
    app.api_id = str(settings.telegramSettings.apiId)
    app.api_hash = settings.telegramSettings.apiHash
    if settings.telegramSettings.botToken:
        app.bot_token = settings.telegramSettings.botToken
    app.session_file_path = settings.telegramSettings.sessionPath

    # 更新文件设置
    app.file_path_prefix = settings.fileSettings.filePathPrefix
    app.hide_file_name = settings.fileSettings.hideFileName
    app.file_name_prefix = settings.fileSettings.fileNamePrefix
    app.media_types = settings.fileSettings.mediaTypes
    app.file_formats = settings.fileSettings.fileFormats
    app.enable_download_txt = settings.fileSettings.enableDownloadTxt
    app.drop_no_audio_video = settings.fileSettings.dropNoAudioVideo

    # 更新性能设置
    app.max_concurrent_transmissions = settings.performanceSettings.maxConcurrentTransmissions
    app.start_timeout = settings.performanceSettings.startTimeout
    config.performance.timeout = settings.performanceSettings.timeout
    config.performance.retry_attempts = settings.performanceSettings.retryAttempts
    config.performance.retry_delay = settings.performanceSettings.retryDelay
    config.performance.max_download_speed = settings.performanceSettings.maxDownloadSpeed

    # 更新安全设置
    app.web_login_secret = settings.securitySettings.webLoginSecret
    app.allowed_user_ids = [int(uid) for uid in settings.securitySettings.allowedUserIds if uid.isdigit()]
    config.security.session_timeout = settings.securitySettings.sessionTimeout
    config.security.max_login_attempts = settings.securitySettings.maxLoginAttempts
    config.security.login_attempt_timeout = settings.securitySettings.loginAttemptTimeout
    config.security.allowed_hosts = settings.securitySettings.allowedHosts

    # 更新数据库设置
    config.database.url = settings.databaseSettings.url
    config.database.echo = settings.databaseSettings.echo
    config.database.pool_size = settings.databaseSettings.poolSize
    config.database.max_overflow = settings.databaseSettings.maxOverflow

    # 更新代理设置
    config.proxy.enabled = settings.proxySettings.enabled
    config.proxy.scheme = settings.proxySettings.type
    config.proxy.hostname = settings.proxySettings.host
    config.proxy.port = settings.proxySettings.port
    config.proxy.username = settings.proxySettings.username
    config.proxy.password = settings.proxySettings.password

    # 更新通知设置
    config.notification.enabled = settings.notificationSettings.enabled
    config.notification.task_completion = settings.notificationSettings.taskCompletion
    config.notification.task_failure = settings.notificationSettings.taskFailure
    config.notification.system_alerts = settings.notificationSettings.systemAlerts
    config.notification.email_enabled = settings.notificationSettings.emailEnabled
    config.notification.email_smtp_server = settings.notificationSettings.emailSmtpServer
    config.notification.email_smtp_port = settings.notificationSettings.emailSmtpPort
    config.notification.email_username = settings.notificationSettings.emailUsername
    config.notification.email_password = settings.notificationSettings.emailPassword
    config.notification.email_from = settings.notificationSettings.emailFrom
    config.notification.email_to = settings.notificationSettings.emailTo


@router.get("", response_model=SettingsResponse)
async def get_settings(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取系统设置"""
    try:
        app = get_app_instance()
        settings = _convert_app_to_system_settings(app, config_manager)

        return create_response(
            success=True,
            data=settings.dict(),
            message="获取设置成功"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取设置失败: {str(e)}"
        )


@router.put("", response_model=SettingsResponse)
async def update_settings(
    settings_update: SettingsUpdateRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """更新系统设置"""
    try:
        app = get_app_instance()

        # 获取当前设置
        current_settings = _convert_app_to_system_settings(app, config_manager)

        # 应用更新
        update_data = settings_update.dict(exclude_unset=True)
        for key, value in update_data.items():
            if hasattr(current_settings, key):
                setattr(current_settings, key, value)

        # 更新配置实例
        _update_app_from_system_settings(app, config_manager, current_settings)

        # 保存配置到文件
        app.update_config(immediate=True)
        config_manager.save_config()

        return create_response(
            success=True,
            data=current_settings.dict(),
            message="设置更新成功"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新设置失败: {str(e)}"
        )


@router.post("/reset")
async def reset_settings(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """重置设置为默认值"""
    try:
        # 重新创建配置实例
        global config_manager, app_instance
        config_manager = Config()
        app_instance = None  # 重置应用实例，下次调用时会重新创建

        app = get_app_instance()
        default_settings = _convert_app_to_system_settings(app, config_manager)

        return create_response(
            success=True,
            data=default_settings.dict(),
            message="设置已重置为默认值"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置设置失败: {str(e)}"
        )


@router.get("/telegram", response_model=Dict[str, Any])
async def get_telegram_settings(
    current_user: User = Depends(get_current_active_user)
):
    """获取Telegram配置"""
    try:
        app = get_app_instance()
        telegram_settings = TelegramSettings(
            apiId=int(app.api_id) if app.api_id and app.api_id.isdigit() else 0,
            apiHash=app.api_hash,
            sessionName="telegram_session",
            botToken=app.bot_token if hasattr(app, 'bot_token') else None,
            sessionPath=app.session_file_path
        )

        return create_response(
            success=True,
            data=telegram_settings.dict(),
            message="获取Telegram设置成功"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Telegram设置失败: {str(e)}"
        )


@router.put("/telegram")
async def update_telegram_settings(
    telegram_settings: TelegramSettings,
    current_user: User = Depends(get_current_active_user)
):
    """更新Telegram配置"""
    try:
        app = get_app_instance()

        # 更新Telegram设置
        app.api_id = str(telegram_settings.apiId)
        app.api_hash = telegram_settings.apiHash
        if telegram_settings.botToken:
            app.bot_token = telegram_settings.botToken
        app.session_file_path = telegram_settings.sessionPath

        # 保存配置
        app.update_config(immediate=True)

        return create_response(
            success=True,
            data=telegram_settings.dict(),
            message="Telegram设置更新成功"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新Telegram设置失败: {str(e)}"
        )
