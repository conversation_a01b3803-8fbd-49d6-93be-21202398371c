import React, { useState, useEffect } from 'react';
import {
  Card,
  Radio,
  Space,
  Typography,
  Button,
  Row,
  Col,
  Tooltip,
  Switch,
  Divider,
  message,
} from 'antd';
import {
  SunOutlined,
  MoonOutlined,
  DesktopOutlined,
  BgColorsOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { presetThemes, getSystemTheme, applyThemeTransition } from '@/utils/theme';

const { Title, Text } = Typography;

interface ThemeSettingsProps {
  currentTheme: 'light' | 'dark' | 'auto';
  currentColorScheme: string;
  onThemeChange: (theme: 'light' | 'dark' | 'auto') => void;
  onColorSchemeChange: (colorScheme: string) => void;
}

const ThemeSettings: React.FC<ThemeSettingsProps> = ({
  currentTheme,
  currentColorScheme,
  onThemeChange,
  onColorSchemeChange,
}) => {
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');
  const [enableTransition, setEnableTransition] = useState(true);

  useEffect(() => {
    // 检测系统主题
    setSystemTheme(getSystemTheme());

    // 监听系统主题变化
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => {
        setSystemTheme(e.matches ? 'dark' : 'light');
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, []);

  const handleThemeChange = (theme: 'light' | 'dark' | 'auto') => {
    if (enableTransition) {
      applyThemeTransition();
    }
    onThemeChange(theme);
    message.success(`已切换到${theme === 'light' ? '亮色' : theme === 'dark' ? '暗色' : '自动'}主题`);
  };

  const handleColorSchemeChange = (colorScheme: string) => {
    if (enableTransition) {
      applyThemeTransition();
    }
    onColorSchemeChange(colorScheme);
    message.success(`已切换到${presetThemes[colorScheme as keyof typeof presetThemes]?.name || colorScheme}主题色`);
  };

  const getCurrentDisplayTheme = () => {
    if (currentTheme === 'auto') {
      return systemTheme;
    }
    return currentTheme;
  };

  return (
    <Card
      title={
        <Space>
          <BgColorsOutlined />
          主题设置
        </Space>
      }
      size="small"
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 主题模式选择 */}
        <div>
          <Title level={5}>主题模式</Title>
          <Radio.Group
            value={currentTheme}
            onChange={(e) => handleThemeChange(e.target.value)}
            style={{ width: '100%' }}
          >
            <Row gutter={[8, 8]}>
              <Col span={8}>
                <Tooltip title="亮色主题">
                  <Radio.Button value="light" style={{ width: '100%', textAlign: 'center' }}>
                    <Space>
                      <SunOutlined />
                      亮色
                    </Space>
                  </Radio.Button>
                </Tooltip>
              </Col>
              <Col span={8}>
                <Tooltip title="暗色主题">
                  <Radio.Button value="dark" style={{ width: '100%', textAlign: 'center' }}>
                    <Space>
                      <MoonOutlined />
                      暗色
                    </Space>
                  </Radio.Button>
                </Tooltip>
              </Col>
              <Col span={8}>
                <Tooltip title={`跟随系统 (当前: ${systemTheme === 'dark' ? '暗色' : '亮色'})`}>
                  <Radio.Button value="auto" style={{ width: '100%', textAlign: 'center' }}>
                    <Space>
                      <DesktopOutlined />
                      自动
                    </Space>
                  </Radio.Button>
                </Tooltip>
              </Col>
            </Row>
          </Radio.Group>
          <Text type="secondary" style={{ fontSize: '12px', marginTop: 8, display: 'block' }}>
            当前显示: {getCurrentDisplayTheme() === 'dark' ? '暗色' : '亮色'}主题
          </Text>
        </div>

        <Divider />

        {/* 主题色选择 */}
        <div>
          <Title level={5}>主题色彩</Title>
          <Radio.Group
            value={currentColorScheme}
            onChange={(e) => handleColorSchemeChange(e.target.value)}
            style={{ width: '100%' }}
          >
            <Row gutter={[8, 8]}>
              {Object.entries(presetThemes).map(([key, theme]) => (
                <Col span={12} key={key}>
                  <Tooltip title={theme.name}>
                    <Radio.Button
                      value={key}
                      style={{
                        width: '100%',
                        textAlign: 'center',
                        borderColor: currentColorScheme === key ? theme.primary : undefined,
                      }}
                    >
                      <Space>
                        <div
                          style={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            backgroundColor: theme.primary,
                            display: 'inline-block',
                          }}
                        />
                        {theme.name}
                      </Space>
                    </Radio.Button>
                  </Tooltip>
                </Col>
              ))}
            </Row>
          </Radio.Group>
        </div>

        <Divider />

        {/* 高级设置 */}
        <div>
          <Title level={5}>高级设置</Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text>主题切换动画</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  启用主题切换时的过渡动画效果
                </Text>
              </div>
              <Switch
                checked={enableTransition}
                onChange={setEnableTransition}
                checkedChildren="开"
                unCheckedChildren="关"
              />
            </div>
          </Space>
        </div>

        <Divider />

        {/* 快速操作 */}
        <div>
          <Title level={5}>快速操作</Title>
          <Space wrap>
            <Button
              size="small"
              icon={<SunOutlined />}
              onClick={() => handleThemeChange('light')}
              type={currentTheme === 'light' ? 'primary' : 'default'}
            >
              切换到亮色
            </Button>
            <Button
              size="small"
              icon={<MoonOutlined />}
              onClick={() => handleThemeChange('dark')}
              type={currentTheme === 'dark' ? 'primary' : 'default'}
            >
              切换到暗色
            </Button>
            <Button
              size="small"
              icon={<DesktopOutlined />}
              onClick={() => handleThemeChange('auto')}
              type={currentTheme === 'auto' ? 'primary' : 'default'}
            >
              跟随系统
            </Button>
          </Space>
        </div>

        {/* 预览区域 */}
        <div>
          <Title level={5}>主题预览</Title>
          <div
            style={{
              padding: 16,
              border: '1px solid #d9d9d9',
              borderRadius: 6,
              backgroundColor: getCurrentDisplayTheme() === 'dark' ? '#141414' : '#ffffff',
              color: getCurrentDisplayTheme() === 'dark' ? '#ffffff' : '#000000',
            }}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong>这是一个主题预览区域</Text>
              <Text type="secondary">次要文本颜色</Text>
              <Button
                type="primary"
                size="small"
                style={{
                  backgroundColor: presetThemes[currentColorScheme as keyof typeof presetThemes]?.primary,
                  borderColor: presetThemes[currentColorScheme as keyof typeof presetThemes]?.primary,
                }}
              >
                主要按钮
              </Button>
            </Space>
          </div>
        </div>
      </Space>
    </Card>
  );
};

export default ThemeSettings;
