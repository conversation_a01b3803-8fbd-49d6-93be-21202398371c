import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  List,
  Switch,
  Typography,
  Popconfirm,
  Modal,
  message,
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SettingOutlined } from '@ant-design/icons';
import type { ForwardRule } from '@/types';

const { Text } = Typography;
const { TextArea } = Input;

interface ForwardRulesProps {
  rules: ForwardRule[];
  onChange?: (rules: ForwardRule[]) => void;
}

const ForwardRules: React.FC<ForwardRulesProps> = ({ rules, onChange }) => {
  const [form] = Form.useForm();
  const [editingRule, setEditingRule] = useState<ForwardRule | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  const handleAddRule = () => {
    setEditingRule(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditRule = (rule: ForwardRule) => {
    setEditingRule(rule);
    form.setFieldsValue(rule);
    setModalVisible(true);
  };

  const handleDeleteRule = (ruleId: string) => {
    const newRules = rules.filter((rule) => rule.id !== ruleId);
    onChange?.(newRules);
    message.success('规则已删除');
  };

  const handleToggleRule = (ruleId: string, isActive: boolean) => {
    const newRules = rules.map((rule) =>
      rule.id === ruleId ? { ...rule, isActive } : rule
    );
    onChange?.(newRules);
  };

  const handleSaveRule = (values: any) => {
    const newRule: ForwardRule = {
      id: editingRule?.id || Date.now().toString(),
      name: values.name,
      sourcePattern: values.sourcePattern,
      targetPattern: values.targetPattern,
      isActive: values.isActive ?? true,
    };

    let newRules: ForwardRule[];
    if (editingRule) {
      newRules = rules.map((rule) => (rule.id === editingRule.id ? newRule : rule));
      message.success('规则已更新');
    } else {
      newRules = [...rules, newRule];
      message.success('规则已添加');
    }

    onChange?.(newRules);
    setModalVisible(false);
    form.resetFields();
  };

  return (
    <Card
      title={
        <Space>
          <SettingOutlined />
          转发规则
        </Space>
      }
      extra={
        <Button type="primary" size="small" icon={<PlusOutlined />} onClick={handleAddRule}>
          添加规则
        </Button>
      }
      size="small"
    >
      <List
        dataSource={rules}
        renderItem={(rule) => (
          <List.Item
            actions={[
              <Switch
                key="toggle"
                size="small"
                checked={rule.isActive}
                onChange={(checked) => handleToggleRule(rule.id, checked)}
              />,
              <Button
                key="edit"
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleEditRule(rule)}
              />,
              <Popconfirm
                key="delete"
                title="确定要删除这个规则吗？"
                onConfirm={() => handleDeleteRule(rule.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button type="text" size="small" icon={<DeleteOutlined />} danger />
              </Popconfirm>,
            ]}
          >
            <List.Item.Meta
              title={
                <Space>
                  <Text strong>{rule.name}</Text>
                  {!rule.isActive && <Text type="secondary">(已禁用)</Text>}
                </Space>
              }
              description={
                <div>
                  {rule.sourcePattern && (
                    <div>
                      <Text type="secondary">源匹配: </Text>
                      <Text code>{rule.sourcePattern}</Text>
                    </div>
                  )}
                  {rule.targetPattern && (
                    <div>
                      <Text type="secondary">目标替换: </Text>
                      <Text code>{rule.targetPattern}</Text>
                    </div>
                  )}
                </div>
              }
            />
          </List.Item>
        )}
        locale={{ emptyText: '暂无转发规则' }}
      />

      <Modal
        title={editingRule ? '编辑转发规则' : '添加转发规则'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSaveRule}>
          <Form.Item
            name="name"
            label="规则名称"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="输入规则名称" />
          </Form.Item>

          <Form.Item
            name="sourcePattern"
            label="源内容匹配"
            tooltip="支持正则表达式，用于匹配需要转发的消息内容"
          >
            <TextArea
              rows={3}
              placeholder="例如: .*\.(jpg|png|gif)$ (匹配图片文件)"
            />
          </Form.Item>

          <Form.Item
            name="targetPattern"
            label="目标内容替换"
            tooltip="转发时替换消息内容的模板，可使用 $1, $2 等引用匹配组"
          >
            <TextArea
              rows={3}
              placeholder="例如: [转发] $1 (在原内容前添加[转发]标记)"
            />
          </Form.Item>

          <Form.Item name="isActive" valuePropName="checked">
            <Switch checkedChildren="启用" unCheckedChildren="禁用" defaultChecked />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingRule ? '更新' : '添加'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default ForwardRules;
