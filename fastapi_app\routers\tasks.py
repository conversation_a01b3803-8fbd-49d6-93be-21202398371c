"""
任务管理API路由
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from ..auth import get_current_active_user, User
from ..database import get_db, Task
from ..utils import create_response

router = APIRouter()


class TaskResponse(BaseModel):
    """任务响应模型"""
    id: str
    name: str
    type: str
    status: str
    progress: int
    createdAt: str
    updatedAt: str
    completedAt: Optional[str] = None


class TaskListResponse(BaseModel):
    """任务列表响应模型"""
    tasks: List[TaskResponse]
    pagination: dict


@router.get("")
async def get_tasks(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取任务列表"""
    try:
        # 构建查询
        query = select(Task)
        if status:
            query = query.where(Task.status == status)
        
        # 计算总数
        count_query = select(func.count(Task.id))
        if status:
            count_query = count_query.where(Task.status == status)
        
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        offset = (page - 1) * limit
        query = query.offset(offset).limit(limit).order_by(Task.created_at.desc())
        
        result = await db.execute(query)
        tasks = result.scalars().all()
        
        # 转换为响应格式
        task_list = []
        for task in tasks:
            task_data = TaskResponse(
                id=task.task_id,
                name=task.name,
                type=task.type,
                status=task.status,
                progress=task.progress,
                createdAt=task.created_at.isoformat() + "Z",
                updatedAt=task.updated_at.isoformat() + "Z",
                completedAt=task.completed_at.isoformat() + "Z" if task.completed_at else None
            )
            task_list.append(task_data)
        
        # 计算分页信息
        pages = (total + limit - 1) // limit
        
        response_data = TaskListResponse(
            tasks=task_list,
            pagination={
                "page": page,
                "limit": limit,
                "total": total,
                "pages": pages
            }
        )
        
        return create_response(
            success=True,
            data=response_data.dict()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务列表失败: {str(e)}"
        )


@router.get("/{task_id}")
async def get_task(
    task_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取单个任务信息"""
    try:
        result = await db.execute(
            select(Task).where(Task.task_id == task_id)
        )
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        task_response = TaskResponse(
            id=task.task_id,
            name=task.name,
            type=task.type,
            status=task.status,
            progress=task.progress,
            createdAt=task.created_at.isoformat() + "Z",
            updatedAt=task.updated_at.isoformat() + "Z",
            completedAt=task.completed_at.isoformat() + "Z" if task.completed_at else None
        )
        
        return create_response(
            success=True,
            data=task_response.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务信息失败: {str(e)}"
        )


@router.post("/{task_id}/cancel")
async def cancel_task(
    task_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """取消任务"""
    try:
        result = await db.execute(
            select(Task).where(Task.task_id == task_id)
        )
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        if task.status in ["completed", "failed", "cancelled"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="任务已完成或已取消，无法取消"
            )
        
        # 更新任务状态
        task.status = "cancelled"
        await db.commit()
        
        return create_response(
            success=True,
            message="任务已取消"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消任务失败: {str(e)}"
        )
