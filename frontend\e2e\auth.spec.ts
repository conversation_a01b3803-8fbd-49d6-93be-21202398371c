import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test.beforeEach(async ({ page }) => {
    // 清除本地存储
    await page.goto('/');
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
  });

  test('should redirect to login page when not authenticated', async ({ page }) => {
    await page.goto('/dashboard');
    
    // 应该重定向到登录页面
    await expect(page).toHaveURL('/login');
    await expect(page.locator('h2')).toContainText('Telegram Media Downloader');
  });

  test('should login successfully with valid credentials', async ({ page }) => {
    await page.goto('/login');
    
    // 填写登录表单
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'admin');
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 等待重定向到仪表板
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('h2')).toContainText('仪表板');
  });

  test('should show error message with invalid credentials', async ({ page }) => {
    await page.goto('/login');
    
    // 填写错误的登录信息
    await page.fill('input[placeholder="用户名"]', 'wrong');
    await page.fill('input[placeholder="密码"]', 'wrong');
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 应该显示错误消息
    await expect(page.locator('.ant-message')).toBeVisible();
  });

  test('should logout successfully', async ({ page }) => {
    // 先登录
    await page.goto('/login');
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'admin');
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL('/dashboard');
    
    // 点击用户头像打开下拉菜单
    await page.click('.ant-avatar');
    
    // 点击退出登录
    await page.click('text=退出登录');
    
    // 应该重定向到登录页面
    await expect(page).toHaveURL('/login');
  });

  test('should persist login state after page refresh', async ({ page }) => {
    // 登录
    await page.goto('/login');
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'admin');
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL('/dashboard');
    
    // 刷新页面
    await page.reload();
    
    // 应该仍然在仪表板页面
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('h2')).toContainText('仪表板');
  });
});
