import React from 'react';
import { Select, Space, Avatar, Typography, Tag } from 'antd';
import { TeamOutlined, SoundOutlined, GlobalOutlined } from '@ant-design/icons';
import type { Group } from '@/types';

const { Text } = Typography;

interface GroupSelectorProps {
  groups: Group[];
  value?: string;
  onChange?: (groupId: string) => void;
  loading?: boolean;
  placeholder?: string;
  multiple?: boolean;
}

const GroupSelector: React.FC<GroupSelectorProps> = ({
  groups,
  value,
  onChange,
  loading = false,
  placeholder = '请选择群组',
  multiple = false,
}) => {
  const getGroupIcon = (type: string) => {
    switch (type) {
      case 'channel':
        return <SoundOutlined />;
      case 'supergroup':
        return <GlobalOutlined />;
      default:
        return <TeamOutlined />;
    }
  };

  const getGroupTypeTag = (type: string) => {
    const typeMap = {
      channel: { color: 'blue', text: '频道' },
      supergroup: { color: 'green', text: '超级群组' },
      group: { color: 'orange', text: '群组' },
    };
    const config = typeMap[type as keyof typeof typeMap] || typeMap.group;
    return (
      <Tag color={config.color} size="small">
        {config.text}
      </Tag>
    );
  };

  const options = groups.map((group) => ({
    label: (
      <Space>
        <Avatar size="small" icon={getGroupIcon(group.type)} />
        <div style={{ flex: 1 }}>
          <div>
            <Text strong>{group.title}</Text>
            {getGroupTypeTag(group.type)}
          </div>
          {group.memberCount && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {group.memberCount} 成员
            </Text>
          )}
        </div>
      </Space>
    ),
    value: group.id,
  }));

  return (
    <Select
      mode={multiple ? 'multiple' : undefined}
      value={value}
      onChange={onChange}
      options={options}
      placeholder={placeholder}
      loading={loading}
      style={{ width: '100%' }}
      optionLabelProp="label"
      showSearch
      filterOption={(input, option) => {
        const group = groups.find((grp) => grp.id === option?.value);
        if (!group) return false;
        return (
          group.title.toLowerCase().includes(input.toLowerCase()) ||
          (group.description && group.description.toLowerCase().includes(input.toLowerCase()))
        );
      }}
      maxTagCount="responsive"
    />
  );
};

export default GroupSelector;
