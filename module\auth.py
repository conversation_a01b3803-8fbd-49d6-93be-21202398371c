"""
认证和授权模块
"""

import os
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from flask import Flask, session, request, current_app
from functools import wraps

try:
    from .database import db, User
except ImportError:
    db = None
    User = None

class AuthManager:
    """认证管理器"""
    
    def __init__(self):
        self.session_timeout = 3600  # 1小时
        self.max_login_attempts = 5
        self.lockout_duration = 300  # 5分钟
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """用户认证"""
        if not User or not db:
            # 模拟模式
            if username == 'admin' and password == 'admin':
                return {
                    'id': '1',
                    'username': 'admin',
                    'role': 'admin',
                    'email': '<EMAIL>',
                }
            return None
        
        try:
            user = User.query.filter_by(username=username).first()
            
            if not user:
                return None
            
            # 检查账号是否被锁定
            if user.locked_until and user.locked_until > datetime.utcnow():
                return None
            
            # 检查账号是否激活
            if not user.is_active:
                return None
            
            # 验证密码
            if not user.check_password(password):
                # 增加登录失败次数
                user.login_attempts += 1
                
                # 如果失败次数超过限制，锁定账号
                if user.login_attempts >= self.max_login_attempts:
                    user.locked_until = datetime.utcnow() + timedelta(seconds=self.lockout_duration)
                
                db.session.commit()
                return None
            
            # 登录成功，重置失败次数
            user.login_attempts = 0
            user.locked_until = None
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            return user.to_dict()
            
        except Exception as e:
            current_app.logger.error(f"Authentication error: {e}")
            return None
    
    def create_session(self, user_data: Dict[str, Any]) -> str:
        """创建用户会话"""
        session['user_id'] = user_data['id']
        session['username'] = user_data['username']
        session['role'] = user_data.get('role', 'user')
        session['login_time'] = datetime.utcnow().isoformat()
        session.permanent = True
        
        # 生成会话令牌
        token = secrets.token_urlsafe(32)
        session['token'] = token
        
        return token
    
    def destroy_session(self):
        """销毁用户会话"""
        session.clear()
    
    def is_authenticated(self) -> bool:
        """检查用户是否已认证"""
        if 'user_id' not in session:
            return False
        
        # 检查会话是否过期
        if 'login_time' in session:
            login_time = datetime.fromisoformat(session['login_time'])
            if datetime.utcnow() - login_time > timedelta(seconds=self.session_timeout):
                self.destroy_session()
                return False
        
        return True
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        if not self.is_authenticated():
            return None
        
        if not User or not db:
            # 模拟模式
            return {
                'id': session['user_id'],
                'username': session['username'],
                'role': session.get('role', 'user'),
            }
        
        try:
            user = User.query.get(session['user_id'])
            return user.to_dict() if user else None
        except Exception:
            return None
    
    def has_permission(self, permission: str) -> bool:
        """检查用户是否有指定权限"""
        user = self.get_current_user()
        if not user:
            return False
        
        role = user.get('role', 'user')
        
        # 管理员拥有所有权限
        if role == 'admin':
            return True
        
        # 定义权限映射
        permissions = {
            'user': [
                'view_dashboard',
                'view_tasks',
                'create_task',
                'update_own_task',
                'delete_own_task',
                'view_accounts',
                'view_groups',
                'view_settings',
            ],
            'admin': [
                'view_dashboard',
                'view_tasks',
                'create_task',
                'update_task',
                'delete_task',
                'view_accounts',
                'create_account',
                'update_account',
                'delete_account',
                'view_groups',
                'view_settings',
                'update_settings',
                'view_logs',
                'manage_users',
            ],
        }
        
        return permission in permissions.get(role, [])
    
    def require_permission(self, permission: str):
        """权限验证装饰器"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                if not self.is_authenticated():
                    return {'error': 'Authentication required'}, 401
                
                if not self.has_permission(permission):
                    return {'error': 'Insufficient permissions'}, 403
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator

# 全局认证管理器实例
auth_manager = AuthManager()

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not auth_manager.is_authenticated():
            from .utils import generate_response
            return generate_response(
                success=False,
                error='Authentication required',
                code='AUTH_REQUIRED'
            ), 401
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """管理员权限验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not auth_manager.is_authenticated():
            from .utils import generate_response
            return generate_response(
                success=False,
                error='Authentication required',
                code='AUTH_REQUIRED'
            ), 401
        
        if not auth_manager.has_permission('manage_users'):
            from .utils import generate_response
            return generate_response(
                success=False,
                error='Admin privileges required',
                code='INSUFFICIENT_PRIVILEGES'
            ), 403
        
        return f(*args, **kwargs)
    return decorated_function

def permission_required(permission: str):
    """权限验证装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not auth_manager.is_authenticated():
                from .utils import generate_response
                return generate_response(
                    success=False,
                    error='Authentication required',
                    code='AUTH_REQUIRED'
                ), 401
            
            if not auth_manager.has_permission(permission):
                from .utils import generate_response
                return generate_response(
                    success=False,
                    error='Insufficient permissions',
                    code='INSUFFICIENT_PRIVILEGES'
                ), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def generate_api_key() -> str:
    """生成API密钥"""
    return secrets.token_urlsafe(32)

def hash_password(password: str) -> str:
    """哈希密码"""
    salt = secrets.token_hex(16)
    password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
    return f"{salt}:{password_hash.hex()}"

def verify_password(password: str, password_hash: str) -> bool:
    """验证密码"""
    try:
        salt, hash_value = password_hash.split(':')
        computed_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return computed_hash.hex() == hash_value
    except Exception:
        return False

def init_auth(app: Flask):
    """初始化认证模块"""
    # 设置会话配置
    app.config['SESSION_COOKIE_SECURE'] = app.config.get('SESSION_COOKIE_SECURE', False)
    app.config['SESSION_COOKIE_HTTPONLY'] = True
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
    app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(seconds=auth_manager.session_timeout)
    
    # 设置密钥
    if not app.config.get('SECRET_KEY'):
        app.config['SECRET_KEY'] = secrets.token_urlsafe(32)
        app.logger.warning("Using generated SECRET_KEY. Set SECRET_KEY in production!")
    
    @app.before_request
    def before_request():
        """请求前处理"""
        # 记录请求日志
        if request.endpoint and not request.endpoint.startswith('static'):
            app.logger.debug(f"{request.method} {request.path} from {request.remote_addr}")
        
        # 检查会话有效性
        if 'user_id' in session and not auth_manager.is_authenticated():
            session.clear()
    
    @app.after_request
    def after_request(response):
        """请求后处理"""
        # 设置安全头
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        return response
