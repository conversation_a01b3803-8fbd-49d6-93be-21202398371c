"""Multi language support"""

# disable pylint: disable = C0301
from enum import Enum


class Language(Enum):
    """Language for ui"""

    EN = 1  # english
    ZH = 2  # china
    RU = 3  # russian
    UA = 4  # ukrainian


_language = Language.EN


def set_language(language: Language):
    """Set Lanaguage"""
    # pylint: disable = W0603
    global _language
    _language = language


translations = {
    "Forward": ["转发", "Переслать", "Переслати"],
    "Total": ["总数", "Всего", "Всього"],
    "Success": ["成功", "Успешно", "Успішно"],
    "Failed": ["失败", "Не удалось", "Не вдалося"],
    "Skipped": ["跳过", "Пропущено", "Пропущено"],
    "Message ID": ["消息ID", "ID сообщения", "ID повідомлення"],
    "Telegram Media Downloader": [
        "电报媒体下载器",
        "Telegram Media Downloader",
        "Telegram Media Downloader",
    ],
    "Version": ["版本", "Версия", "Версія"],
    "Downloading": ["下载", "Скачивание", "Скачування"],
    "Available commands:": ["可用命令:", "Доступные команды:", "Доступні команди:"],
    "Show available commands": [
        "显示可用命令",
        "Показать доступные команды",
        "Показати доступні команди",
    ],
    "Download messages": ["下载消息", "Скачать сообщения", "Скачати повідомлення"],
    "Forward messages": ["转发消息", "Переслать сообщения", "Переслати повідомлення"],
    "Listen for forwarded messages": [
        "监听转发消息",
        "Прослушивать пересланные сообщения",
        "Прослуховувати переслані повідомлення",
    ],
    "Set language": ["设置语言", "Установить язык", "Встановити мову"],
    "**Note**: 1 means the start of the entire chat": [
        "**注意**: 1表示整个聊天的开始",
        "**Примечание**: 1 означает начало всего чата",
        "**Увага**: 1 означає початок всього чату",
    ],
    "0 means the end of the entire chat": [
        "0表示整个聊天的结束",
        "0 означает конец всего чата",
        "0 означає кінець всього чату",
    ],
    "means optional, not required": [
        "表示可选项，非必填",
        "означает необязательный параметр",
        "означає необов'язковий параметр",
    ],
    "To download the video, use the method to directly enter /download to view": [
        "下载视频，使用方法直接输入/download查看",
        "Чтобы скачать видео, введите /download для просмотра",
        "Щоб скачати відео, введіть /download для перегляду",
    ],
    "Forward video, use the method to directly enter /forward to view": [
        "转发视频，使用方法直接输入/forward查看",
        "Переслать видео, введите /forward для просмотра",
        "Переслати відео, введіть /forward для перегляду",
    ],
    "Listen forward, use the method to directly enter /listen_forward to view": [
        "监控转发，使用方法直接输入/listen_forward查看",
        "Слушать пересылку, введите /listen_forward для просмотра",
        "Слухати пересилання, введіть /listen_forward для перегляду",
    ],
    "Add download filter, use the method to directly enter /add_filter to view": [
        "添加下载过滤器",
        "Добавить фильтр загрузки, используйте метод, чтобы непосредственно ввести /add_filter для просмотра",
        "Додати фільтр завантаження, використовуйте метод, щоб безпосередньо ввести /add_filter для перегляду",
    ],
    "Help": ["帮助", "Помощь", "Допомога"],
    "Invalid command format": [
        "无效的命令格式",
        "Неверный формат команды",
        "Невірний формат команди",
    ],
    "Invalid command format. Please use /set_language en/ru/zh/ua": [
        "无效的命令格式。请使用 /set_language en/ru/zh/ua",
        "Неверный формат команды. Пожалуйста, используйте /set_language en/ru/zh/ua",
        "Невірний формат команди. Будь ласка, використовуйте /set_language en/ru/zh/ua",
    ],
    "Language set to English": [
        "语言设置为中文",
        "Выбран английский язык",
        "Обрано англійську мову",
    ],
    "Language set to": [
        "语言设置为",
        "Выбран язык",
        "Обрано мову",
    ],
    "Invalid command format. Please use /add_filter your filter": [
        "无效的命令格式。请使用 /add_filter 你的过滤规则",
        "Неверный формат команды. Пожалуйста, используйте /add_filter ВашФильтр",
        "Невірний формат команди. Будь ласка, використовуйте /add_filter ВашФільтр",
    ],
    "Add download filter": [
        "添加下载过滤器",
        "Добавить фильтр скачивания",
        "Додати фільтр скачування",
    ],
    "Check error, please add again": [
        "检验错误,请重新添加",
        "Ошибка проверки, пожалуйста, добавьте еще раз",
        "Помилка перевірки, будь ласка, додайте ще раз",
    ],
    "Direct download, directly forward the message to your robot": [
        "直接下载，直接转发消息给你的机器人",
        "Скачивание напрямую, пересылка сообщения напрямую вашему роботу",
        "Безпосереднє скачування, безспесередня пересилка повідомлення вашому роботу",
    ],
    "Directly download a single message": [
        "直接下载单条消息",
        "Прямое скачивание одного сообщения",
        "Безпосереднє скачування одного повідомлення",
    ],
    "From": ["从", "От", "Від"],
    "download": ["下载", "скачать", "скачати"],
    "error": ["错误", "ошибка", "помилка"],
    "Parameter error, please enter according to the reference format": [
        "参数错误,请根据参考格式输入",
        "Ошибка параметра, введите в соответствии с форматом ссылки",
        "Помилка параметра, введіть відповідно до формату посилання",
    ],
    "Download all messages of common group": [
        "下载公共群组的所有消息",
        "Скачать все сообщения общей группы",
        "Скачати всі повідомлення спільної групи",
    ],
    "The private group (channel) link is a random group message link": [
        "私密群组(频道) 链接为随便复制一条群组消息链接",
        "Ссылка на частную группу (канал) - это ссылка на случайное сообщение группы",
        "Посилання на приватну групу (канал) - це посилання на випадкове повідомлення групи",
    ],
    "The download starts from the N message to the end of the M message": [
        "下载从第N条消息开始的到第M条信息结束",
        "Скачивание начинается с сообщения N до конца сообщения M",
        "Скачування починається з повідомлення N до кінця повідомлення M",
    ],
    "When M is 0, it means the last message. The filter is optional": [
        "M为0的时候表示到最后一条信息,过滤器为可选",
        "Когда M равно 0, это означает последнее сообщение. Фильтр необязателен",
        "Коли M дорівнює 0, це означає останнє повідомлення. Фільтр необов'язковий",
    ],
    "chat input error, please enter the channel or group link": [
        "chat输入错误，请输入频道或群组的链接",
        "Ошибка ввода чата, введите ссылку на канал или группу",
        "Помилка введеня чату, введіть посилання на канал або групу",
    ],
    "Error type": ["错误类型", "Тип ошибки", "Тип помилки"],
    "Exception message": ["异常消息", "Сообщение исключения", "Повідомлення винятка"],
    "Invalid chat link": [
        "无效的聊天链接",
        "Ошибочная ссылка на чат",
        "Помилкове посилання на чат",
    ],
    "Cannot be forwarded to this bot, will cause an infinite loop": [
        "不能转发给该机器人，会导致无限循环",
        "Невозможно переслать этому боту, это вызовет бесконечный цикл",
        "Неможливо переслати цьому боту, це спричинить безкінечний цикл",
    ],
    "Please use": ["请使用", "Пожалуйста, используйте", "Будь ласка, використовуйте"],
    "Filter": ["过滤器", "Фильтр", "Фільтр"],
    "Error forwarding message": [
        "失败的转发消息",
        "Ошибка пересылки сообщения",
        "Помилка пересилки повідомлення",
    ],
    "file reference expired, refetching": [
        "文件引用过期,重新获取中",
        "Ссылка на файл истекла, повторное получение",
        "Посилання на файл минуло, повторне отримання",
    ],
    "file reference expired for 3 retries, download skipped": [
        "文件引用过期重试超过3次,跳过下载",
        "Ссылка на файл истекла после 3 попыток, загрузка пропущена",
        "Посилання на файл минуло після 3 спроб, завантаження пропущено",
    ],
    "Timeout Error occurred when downloading Message": [
        "下载消息超时错误",
        "Ошибка времени ожидания при скачивании сообщения",
        "Помилка часу очікування при скачуванні повідомлення",
    ],
    "retrying": ["重试", "повторная попытка", "повторна спроба"],
    "seconds": ["秒", "секунд", "секунд"],
    "Timing out after 3 reties, download skipped": [
        "超时重试超过3次,跳过下载",
        "Истекло время ожидания после 3 попыток, загрузка пропущена",
        "Час очікування закінчився після 3 спроб, завантаження пропущено",
    ],
    "could not be downloaded due to following exception": [
        "无法下载,因为以下异常",
        "не может быть скачен по следующей причине",
        "не може бути скачаний з наступної причини",
    ],
    "Downloading files failed during last run": [
        "下载最后一次运行失败的文件",
        "Скачивание файлов не удалось во время последнего запуска",
        "Скачування файлів не вдалося під час останнього запуску",
    ],
    "Successfully started (Press Ctrl+C to stop)": [
        "成功启动(按Ctrl+C停止)",
        "Запуск успешный (нажмите Ctrl + C для остановки)",
        "Запуск успішний (натисніть Ctrl + C для зупинки)",
    ],
    "KeyboardInterrupt": ["键盘中断", "KeyboardInterrupt", "KeyboardInterrupt"],
    "update config": ["更新配置", "обновить конфигурацию", "оновити конфігурацію"],
    "Updated last read message_id to config file": [
        "更新最后阅读消息ID到配置文件",
        "Обновлен идентификатор последнего прочитанного сообщения в конфигурационном файле",
        "Оновлено ідентифікатор останнього прочитаного повідомлення у конфігураційному файлі",
    ],
    "total download": ["总下载", "всего скачено", "всього скачано"],
    "total upload file": ["总上传文件", "всего скаченных файлов", "всього скачаних файлів"],
    "Stopped": ["停止", "остановлено", "зупинено"],
    "already download,download skipped": [
        "已下载,已跳过下载",
        "уже скачен, скачивание пропущена",
        "вже скачан, скачування пропущено",
    ],
    "Media downloaded with wrong size": [
        "媒体下载错误的大小",
        "Медиафайл скачен с неправильным размером",
        "Медіафайл скачано з неправильним розміром",
    ],
    "actual": ["实际", "фактический", "фактичний"],
    "file name": ["文件名", "имя файла", "ім'я файлу"],
    "Successfully downloaded": ["成功下载", "Успешно скачано", "Успішно скачано"],
    "Get group and user info from message link": [
        "从消息链接中获取群组和用户信息",
        "Получить информацию о группе и пользователе по ссылке на сообщение",
        "Отримайте інформацію про групу та користувача за посиланням у повідомленні",
    ],
    "Upload Progresses": ["上传进度", "Прогресс загрузки", "Прогрес завантаження"],
    "Download Progresses": ["下载进度", "Прогресс скачивания", "Прогрес завантаження"],
    "New Version": ["新版本", "новая версия", "нова версія"],
    "Stop bot download or forward": [
        "停止机器人下载或转发",
        "Остановить загрузку или пересылку ботом",
        "Зупинити завантаження або пересилання ботом",
    ],
    "Forward a specific media to a comment section": [
        "将特定媒体转发至评论",
        "Переслать определенное медиа в комментарии",
        "Переслати конкретне медіа в коментарі",
    ],
    "Add replace advertisement filter": [
        "添加删除广告过滤器",
        "Добавить фильтр рекламы",
        "Додати фільтр реклами",
    ],
    "Remove replace advertisement filter": [
        "移除删除广告过滤器",
        "Удалить фильтр рекламы",
        "Видалити фільтр реклами",
    ],
    "Add filter advertisement filter": [
        "添加过滤广告过滤器",
        "Добавить фильтр рекламы",
        "Додати фільтр реклами",
    ],
    "Remove filter advertisement filter": [
        "移除过滤广告过滤器",
        "Удалить фильтр рекламы",
        "Видалити фільтр реклами",
    ],
    "Set add advertisement": [
        "设置添加广告",
        "Установить рекламу",
        "Встановити рекламу",
    ],
    "Task Management": [
        "任务管理",
        "Управление задачами",
        "Управління завданнями",
    ],
    "Choose what you want to manage:": [
        "选择你想要管理的内容:",
        "Выберите, чем хотите управлять:",
        "Виберіть, чим хочете керувати:",
    ],
    "Manage listen forward tasks and filters": [
        "管理监听转发任务和过滤器",
        "Управление задачами прослушивания пересылки и фильтрами",
        "Управління завданнями прослуховування пересилання та фільтрами",
    ],
    "Listen Forward Tasks": [
        "监听转发任务",
        "Задачи прослушивания пересылки",
        "Завдання прослуховування пересилання",
    ],
    "Advertisement Filters": [
        "广告过滤器",
        "Фильтры рекламы",
        "Фільтри реклами",
    ],
    "Replace Ad Filters": [
        "替换广告过滤器",
        "Фильтры замены рекламы",
        "Фільтри заміни реклами",
    ],
    "Add Advertisement Configurations": [
        "添加广告配置",
        "Конфигурации добавления рекламы",
        "Конфігурації додавання реклами",
    ],
    "No active listen forward tasks.": [
        "没有活跃的监听转发任务。",
        "Нет активных задач прослушивания пересылки.",
        "Немає активних завдань прослуховування пересилання.",
    ],
    "Use /listen_forward to add new tasks.": [
        "使用 /listen_forward 添加新任务。",
        "Используйте /listen_forward для добавления новых задач.",
        "Використовуйте /listen_forward для додавання нових завдань.",
    ],
    "No advertisement filters configured.": [
        "没有配置广告过滤器。",
        "Нет настроенных фильтров рекламы.",
        "Немає налаштованих фільтрів реклами.",
    ],
    "Use /add_filter_ad to add filters.": [
        "使用 /add_filter_ad 添加过滤器。",
        "Используйте /add_filter_ad для добавления фильтров.",
        "Використовуйте /add_filter_ad для додавання фільтрів.",
    ],
    "No replace filters configured.": [
        "没有配置替换过滤器。",
        "Нет настроенных фильтров замены.",
        "Немає налаштованих фільтрів заміни.",
    ],
    "Use /add_replace_ad to add filters.": [
        "使用 /add_replace_ad 添加过滤器。",
        "Используйте /add_replace_ad для добавления фильтров.",
        "Використовуйте /add_replace_ad для додавання фільтрів.",
    ],
    "No add advertisement configurations.": [
        "没有添加广告配置。",
        "Нет конфигураций добавления рекламы.",
        "Немає конфігурацій додавання реклами.",
    ],
    "Use /set_add_ad to add configurations.": [
        "使用 /set_add_ad 添加配置。",
        "Используйте /set_add_ad для добавления конфигураций.",
        "Використовуйте /set_add_ad для додавання конфігурацій.",
    ],
    "Task deleted successfully": [
        "任务删除成功",
        "Задача успешно удалена",
        "Завдання успішно видалено",
    ],
    "Task not found": [
        "任务未找到",
        "Задача не найдена",
        "Завдання не знайдено",
    ],
    "Filter deleted": [
        "过滤器已删除",
        "Фильтр удален",
        "Фільтр видалено",
    ],
    "Filter not found": [
        "过滤器未找到",
        "Фильтр не найден",
        "Фільтр не знайдено",
    ],
    "Replace filter deleted": [
        "替换过滤器已删除",
        "Фильтр замены удален",
        "Фільтр заміни видалено",
    ],
    "Advertisement configuration deleted": [
        "广告配置已删除",
        "Конфигурация рекламы удалена",
        "Конфігурацію реклами видалено",
    ],
    "Configuration not found": [
        "配置未找到",
        "Конфигурация не найдена",
        "Конфігурацію не знайдено",
    ],
    "Choose what you want to do:": [
        "选择你想要执行的操作:",
        "Выберите, что хотите сделать:",
        "Виберіть, що хочете зробити:",
    ],
    "Delete": [
        "删除",
        "Удалить",
        "Видалити",
    ],
    "View": [
        "查看",
        "Просмотр",
        "Перегляд",
    ],
    "Add New": [
        "添加新的",
        "Добавить новый",
        "Додати новий",
    ],
    "To add a new advertisement filter, use:": [
        "要添加新的广告过滤器，请使用:",
        "Чтобы добавить новый фильтр рекламы, используйте:",
        "Щоб додати новий фільтр реклами, використовуйте:",
    ],
    "Example:": [
        "示例:",
        "Пример:",
        "Приклад:",
    ],
    "The filter will match any message containing the specified text.": [
        "过滤器将匹配包含指定文本的任何消息。",
        "Фильтр будет соответствовать любому сообщению, содержащему указанный текст.",
        "Фільтр відповідатиме будь-якому повідомленню, що містить вказаний текст.",
    ],
    "To add a new replace filter, use:": [
        "要添加新的替换过滤器，请使用:",
        "Чтобы добавить новый фильтр замены, используйте:",
        "Щоб додати новий фільтр заміни, використовуйте:",
    ],
    "This will extract the specified text from the message and use it as a replacement filter.": [
        "这将从消息中提取指定的文本并将其用作替换过滤器。",
        "Это извлечет указанный текст из сообщения и использует его как фильтр замены.",
        "Це витягне вказаний текст з повідомлення та використає його як фільтр заміни.",
    ],
    "To add a new advertisement configuration, use:": [
        "要添加新的广告配置，请使用:",
        "Чтобы добавить новую конфигурацию рекламы, используйте:",
        "Щоб додати нову конфігурацію реклами, використовуйте:",
    ],
    "This will add the specified advertisement text to messages in the target channel.": [
        "这将在目标频道的消息中添加指定的广告文本。",
        "Это добавит указанный рекламный текст к сообщениям в целевом канале.",
        "Це додасть вказаний рекламний текст до повідомлень у цільовому каналі.",
    ],
    "Unknown": [
        "未知",
        "Неизвестно",
        "Невідомо",
    ],
    "Status": [
        "状态",
        "Статус",
        "Статус",
    ],
    "Active": [
        "活跃",
        "Активен",
        "Активний",
    ],
    "Stopped": [
        "已停止",
        "Остановлен",
        "Зупинено",
    ],
    "Tasks": [
        "任务",
        "Задачи",
        "Завдання",
    ],
    "Filters": [
        "过滤器",
        "Фильтры",
        "Фільтри",
    ],
    "Back": [
        "返回",
        "Назад",
        "Назад",
    ],
    "Config": [
        "配置",
        "Конфигурация",
        "Конфігурація",
    ],
    "Configs": [
        "配置",
        "Конфигурации",
        "Конфігурації",
    ],
    "Ad": [
        "广告",
        "Реклама",
        "Реклама",
    ],
    "None": [
        "无",
        "Нет",
        "Немає",
    ],
    "Direct download...": [
        "直接下载中...",
        "Прямое скачивание...",
        "Безпосереднє скачування...",
    ],
    "Forwarding message, please wait...": [
        "转发消息中，请稍候...",
        "Пересылка сообщения, пожалуйста, подождите...",
        "Пересилання повідомлення, будь ласка, зачекайте...",
    ],
    "Note that the robot may not be in the target group, use the user account to forward": [
        "注意：机器人可能不在目标群组中，使用用户账户转发",
        "Обратите внимание, что бот может не находиться в целевой группе, используйте пользовательский аккаунт для пересылки",
        "Зверніть увагу, що бот може не знаходитися в цільовій групі, використовуйте користувацький акаунт для пересилання",
    ],
    "Github": [
        "Github",
        "Github",
        "Github",
    ],
    "Join us": [
        "加入我们",
        "Присоединяйтесь",
        "Приєднуйтесь",
    ],
    # New translations for regex filter functionality
    "Basic Examples": [
        "基础示例",
        "Основные примеры",
        "Основні приклади",
    ],
    "Multi-line Matching Examples": [
        "多行匹配示例",
        "Примеры многострочного соответствия",
        "Приклади багаторядкового відповідання",
    ],
    "Advanced Patterns": [
        "高级模式",
        "Расширенные шаблоны",
        "Розширені шаблони",
    ],
    "Matches subscription ads with usernames": [
        "匹配带用户名的订阅广告",
        "Соответствует рекламе подписок с именами пользователей",
        "Відповідає рекламі підписок з іменами користувачів",
    ],
    "Matches announcement patterns with arrows and usernames": [
        "匹配带箭头和用户名的公告模式",
        "Соответствует шаблонам объявлений со стрелками и именами пользователей",
        "Відповідає шаблонам оголошень зі стрілками та іменами користувачів",
    ],
    "Matches channel promotions with Telegram links": [
        "匹配带Telegram链接的频道推广",
        "Соответствует продвижению каналов с ссылками Telegram",
        "Відповідає просуванню каналів з посиланнями Telegram",
    ],
    "Matches submission requests with contact info": [
        "匹配带联系信息的投稿请求",
        "Соответствует запросам на подачу заявок с контактной информацией",
        "Відповідає запитам на подання заявок з контактною інформацією",
    ],
    "Matches any character including newlines": [
        "匹配包括换行符在内的任何字符",
        "Соответствует любому символу, включая переносы строк",
        "Відповідає будь-якому символу, включаючи переноси рядків",
    ],
    "Non-greedy matching (shortest match)": [
        "非贪婪匹配（最短匹配）",
        "Жадное соответствие (кратчайшее соответствие)",
        "Жадібне відповідання (найкоротше відповідання)",
    ],
    "Matches one or more whitespace characters": [
        "匹配一个或多个空白字符",
        "Соответствует одному или нескольким символам пробела",
        "Відповідає одному або кільком символам пробілу",
    ],
    "Matches entire lines containing subscription": [
        "匹配包含订阅的整行",
        "Соответствует целым строкам, содержащим подписку",
        "Відповідає цілим рядкам, що містять підписку",
    ],
    "Duplicate patterns will not be added.": [
        "重复的模式不会被添加。",
        "Дублирующиеся шаблоны не будут добавлены.",
        "Дублювання шаблонів не буде додано.",
    ],
    "Pattern already exists": [
        "模式已存在",
        "Шаблон уже существует",
        "Шаблон уже існує",
    ],
    # Regex testing functionality translations
    "Usage": [
        "用法",
        "Использование",
        "Використання",
    ],
    "This command helps you test regex patterns before adding them as filters.": [
        "此命令帮助您在添加正则表达式为过滤器之前测试它们。",
        "Эта команда поможет вам протестировать регулярные выражения перед добавлением их в качестве фильтров.",
        "Ця команда допоможе вам протестувати регулярні вирази перед додаванням їх як фільтри.",
    ],
    "Invalid regex pattern": [
        "无效的正则表达式模式",
        "Недопустимый шаблон регулярного выражения",
        "Неприпустимий шаблон регулярного виразу",
    ],
    "Please check your regex syntax and try again.": [
        "请检查您的正则表达式语法并重试。",
        "Пожалуйста, проверьте синтаксис регулярного выражения и попробуйте снова.",
        "Будь ласка, перевірте синтаксис регулярного виразу та спробуйте знову.",
    ],
    "Invalid message link": [
        "无效的消息链接",
        "Недопустимая ссылка на сообщение",
        "Неприпустиме посилання на повідомлення",
    ],
    "Please provide a valid Telegram message link.": [
        "请提供有效的Telegram消息链接。",
        "Пожалуйста, укажите действительную ссылку на сообщение Telegram.",
        "Будь ласка, надайте дійсне посилання на повідомлення Telegram.",
    ],
    "Failed to retrieve message": [
        "获取消息失败",
        "Не удалось получить сообщение",
        "Не вдалося отримати повідомлення",
    ],
    "Message ID": [
        "消息ID",
        "ID сообщения",
        "ID повідомлення",
    ],
    "Make sure the bot has access to the source chat.": [
        "确保机器人能够访问源聊天。",
        "Убедитесь, что бот имеет доступ к исходному чату.",
        "Переконайтеся, що бот має доступ до вихідного чату.",
    ],
    "Message has no text content to filter": [
        "消息没有可过滤的文本内容",
        "В сообщении нет текстового содержимого для фильтрации",
        "У повідомленні немає текстового змісту для фільтрації",
    ],
    "This message contains only media without caption or text.": [
        "此消息仅包含媒体，没有标题或文本。",
        "Это сообщение содержит только медиа без заголовка или текста.",
        "Це повідомлення містить тільки медіа без заголовка або тексту.",
    ],
    "Pattern matched - text was modified": [
        "模式匹配 - 文本已修改",
        "Шаблон совпал - текст был изменен",
        "Шаблон співпав - текст було змінено",
    ],
    "Pattern did not match - no changes": [
        "模式不匹配 - 无更改",
        "Шаблон не совпал - изменений нет",
        "Шаблон не співпав - змін немає",
    ],
    "Regex Filter Test Results": [
        "正则表达式过滤器测试结果",
        "Результаты тестирования фильтра регулярных выражений",
        "Результати тестування фільтра регулярних виразів",
    ],
    "Regex Pattern": [
        "正则表达式模式",
        "Шаблон регулярного выражения",
        "Шаблон регулярного виразу",
    ],
    "Matches Found": [
        "找到匹配项",
        "Найдено совпадений",
        "Знайдено збігів",
    ],
    "Filtered Text": [
        "过滤后的文本",
        "Отфильтрованный текст",
        "Фільтрований текст",
    ],
    "Empty after filtering": [
        "过滤后为空",
        "Пустой после фильтрации",
        "Порожній після фільтрації",
    ],
    "Matched Content": [
        "匹配的内容",
        "Соответствующий контент",
        "Відповідний контент",
    ],
    "and": [
        "和",
        "и",
        "і",
    ],
    "more matches": [
        "更多匹配项",
        "больше совпадений",
        "більше збігів",
    ],
    "Add this regex filter": [
        "添加此正则表达式过滤器",
        "Добавить этот фильтр регулярных выражений",
        "Додати цей фільтр регулярних виразів",
    ],
    "Pattern expired, please test again": [
        "模式已过期，请重新测试",
        "Шаблон истек, пожалуйста, протестируйте снова",
        "Шаблон закінчився, будь ласка, протестуйте знову",
    ],
    "Pattern already exists in filters": [
        "模式已存在于过滤器中",
        "Шаблон уже существует в фильтрах",
        "Шаблон уже існує у фільтрах",
    ],
    "Regex filter added successfully!": [
        "正则表达式过滤器添加成功！",
        "Фильтр регулярных выражений успешно добавлен!",
        "Фільтр регулярних виразів успішно додано!",
    ],
    "Added Pattern": [
        "已添加模式",
        "Добавленный шаблон",
        "Доданий шаблон",
    ],
    "You can manage all filters using /manage_tasks": [
        "您可以使用 /manage_tasks 管理所有过滤器",
        "Вы можете управлять всеми фильтрами с помощью /manage_tasks",
        "Ви можете керувати всіма фільтрами за допомогою /manage_tasks",
    ],
    "Error processing request": [
        "处理请求时出错",
        "Ошибка обработки запроса",
        "Помилка обробки запиту",
    ],
    "Please check the message link and try again.": [
        "请检查消息链接并重试。",
        "Пожалуйста, проверьте ссылку на сообщение и попробуйте снова.",
        "Будь ласка, перевірте посилання на повідомлення та спробуйте знову.",
    ],
    "Use /check_regex to test patterns first!": [
        "请先使用 /check_regex 测试模式！",
        "Сначала используйте /check_regex для тестирования шаблонов!",
        "Спочатку використовуйте /check_regex для тестування шаблонів!",
    ],
}


def _t(text: str):
    """Get translation
    Parameters
    ----------
    text : str
    language : str
    Returns
    -------
    str
    """
    if _language is Language.EN:
        return text

    if text in translations:
        return translations[text][_language.value - 2]

    return text
