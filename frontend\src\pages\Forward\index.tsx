import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  message,
  InputNumber,
  List,
  Tabs,
} from 'antd';
import { PlusOutlined, ShareAltOutlined } from '@ant-design/icons';
import AccountSelector from '@/components/AccountSelector';
import GroupSelector from '@/components/GroupSelector';
import TaskCard from '@/components/TaskCard';
import ForwardRules from '@/components/ForwardRules';
import { useTaskStore } from '@/store/taskStore';
import type { Account, Group, ForwardTask, TaskType, ForwardRule } from '@/types';

const { Title } = Typography;
const { TextArea } = Input;

const Forward: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [groups, setGroups] = useState<Group[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<string>();
  const [forwardRules, setForwardRules] = useState<ForwardRule[]>([]);

  const {
    tasks,
    isLoading: tasksLoading,
    createTask,
    startTask,
    pauseTask,
    stopTask,
    deleteTask,
    fetchTasks,
    getTasksByType,
  } = useTaskStore();

  const forwardTasks = getTasksByType('forward' as TaskType);

  // 模拟数据
  useEffect(() => {
    // 模拟账号数据
    setAccounts([
      {
        id: '1',
        name: '主账号',
        phone: '+86 138****1234',
        isActive: true,
        lastLogin: '2024-01-15 10:30:00',
      },
      {
        id: '2',
        name: '备用账号',
        phone: '+86 139****5678',
        isActive: true,
        lastLogin: '2024-01-14 15:20:00',
      },
    ]);

    // 获取任务列表
    fetchTasks();

    // 模拟转发规则
    setForwardRules([
      {
        id: '1',
        name: '图片转发规则',
        sourcePattern: '.*\\.(jpg|png|gif|jpeg)$',
        targetPattern: '[图片分享] $1',
        isActive: true,
      },
      {
        id: '2',
        name: '视频转发规则',
        sourcePattern: '.*\\.(mp4|avi|mkv)$',
        targetPattern: '[视频分享] $1',
        isActive: false,
      },
    ]);
  }, [fetchTasks]);

  // 当选择账号时，获取该账号的群组列表
  useEffect(() => {
    if (selectedAccount) {
      // 模拟群组数据
      setGroups([
        {
          id: 'group1',
          title: '技术交流群',
          type: 'supergroup',
          memberCount: 1250,
          description: '技术讨论和资源分享',
        },
        {
          id: 'channel1',
          title: '资源分享频道',
          type: 'channel',
          memberCount: 5680,
          description: '各种学习资源和工具分享',
        },
        {
          id: 'group2',
          title: '项目协作群',
          type: 'group',
          memberCount: 45,
          description: '项目开发协作讨论',
        },
        {
          id: 'channel2',
          title: '通知频道',
          type: 'channel',
          memberCount: 2340,
          description: '重要通知和公告',
        },
      ]);
    } else {
      setGroups([]);
    }
  }, [selectedAccount]);

  const handleCreateTask = async (values: any) => {
    setLoading(true);
    try {
      const taskData: Partial<ForwardTask> = {
        type: 'forward' as TaskType,
        accountId: values.accountId,
        sourceGroupId: values.sourceGroupId,
        targetGroupId: values.targetGroupId,
        startMessageId: values.startMessageId,
        endMessageId: values.endMessageId,
        forwardRules: forwardRules.filter(rule => rule.isActive),
        progress: 0,
        totalFiles: 0,
        completedFiles: 0,
        failedFiles: 0,
      };

      await createTask(taskData);
      message.success('转发任务创建成功！');
      form.resetFields();
    } catch (error) {
      message.error('创建任务失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTaskAction = async (action: string, taskId: string) => {
    try {
      switch (action) {
        case 'start':
          await startTask(taskId);
          message.success('任务已开始');
          break;
        case 'pause':
          await pauseTask(taskId);
          message.success('任务已暂停');
          break;
        case 'stop':
          await stopTask(taskId);
          message.success('任务已停止');
          break;
        case 'delete':
          await deleteTask(taskId);
          message.success('任务已删除');
          break;
      }
    } catch (error) {
      message.error(`操作失败: ${error}`);
    }
  };

  const tabItems = [
    {
      key: 'create',
      label: '创建转发任务',
      children: (
        <Card size="small">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleCreateTask}
          >
            <Form.Item
              name="accountId"
              label="选择账号"
              rules={[{ required: true, message: '请选择账号' }]}
            >
              <AccountSelector
                accounts={accounts}
                onChange={setSelectedAccount}
                placeholder="选择要使用的Telegram账号"
              />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="sourceGroupId"
                  label="源群组"
                  rules={[{ required: true, message: '请选择源群组' }]}
                >
                  <GroupSelector
                    groups={groups}
                    placeholder="选择消息来源群组"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="targetGroupId"
                  label="目标群组"
                  rules={[{ required: true, message: '请选择目标群组' }]}
                >
                  <GroupSelector
                    groups={groups}
                    placeholder="选择转发目标群组"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={8}>
              <Col span={12}>
                <Form.Item
                  name="startMessageId"
                  label="起始消息ID"
                  rules={[{ required: true, message: '请输入起始消息ID' }]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="1"
                    min={1}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="endMessageId"
                  label="结束消息ID"
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="最新"
                    min={1}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<PlusOutlined />}
                block
              >
                创建转发任务
              </Button>
            </Form.Item>
          </Form>
        </Card>
      ),
    },
    {
      key: 'rules',
      label: '转发规则',
      children: (
        <ForwardRules
          rules={forwardRules}
          onChange={setForwardRules}
        />
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>
        <ShareAltOutlined /> 转发管理
      </Title>

      <Row gutter={[16, 16]}>
        {/* 创建转发任务和规则管理 */}
        <Col xs={24} lg={10}>
          <Tabs items={tabItems} />
        </Col>

        {/* 任务列表 */}
        <Col xs={24} lg={14}>
          <Card title="转发任务列表" size="small">
            <List
              loading={tasksLoading}
              dataSource={forwardTasks}
              renderItem={(task) => (
                <List.Item style={{ padding: '8px 0' }}>
                  <TaskCard
                    task={task}
                    onStart={(id) => handleTaskAction('start', id)}
                    onPause={(id) => handleTaskAction('pause', id)}
                    onStop={(id) => handleTaskAction('stop', id)}
                    onDelete={(id) => handleTaskAction('delete', id)}
                  />
                </List.Item>
              )}
              locale={{ emptyText: '暂无转发任务' }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Forward;
