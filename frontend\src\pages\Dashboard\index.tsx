import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Progress,
  List,
  Button,
  Space,
  Badge,
  Table,
  Tag,
  Tooltip,
  Alert,
} from 'antd';
import {
  DownloadOutlined,
  ShareAltOutlined,
  SoundOutlined,
  CheckCircleOutlined,
  Clock<PERSON>ircleOutlined,
  Exclamation<PERSON>ircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  DashboardOutlined,
  ThunderboltOutlined,
  CloudServerOutlined,
  WifiOutlined,
} from '@ant-design/icons';
import { useTaskStore } from '@/store/taskStore';
import { useWebSocket } from '@/hooks/useWebSocket';
import TaskCard from '@/components/TaskCard';
import type { Task, TaskStatus } from '@/types';

const { Title, Text } = Typography;

interface SystemStatus {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkSpeed: string;
  uptime: string;
}

const Dashboard: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    cpuUsage: 45,
    memoryUsage: 68,
    diskUsage: 32,
    networkSpeed: '1.2 MB/s',
    uptime: '2天 14小时 32分钟',
  });

  const {
    tasks,
    activeTasks,
    isLoading,
    fetchTasks,
    startTask,
    pauseTask,
    stopTask,
    deleteTask,
  } = useTaskStore();

  const { isConnected } = useWebSocket();

  useEffect(() => {
    fetchTasks();

    // 模拟系统状态更新
    const interval = setInterval(() => {
      setSystemStatus(prev => ({
        ...prev,
        cpuUsage: Math.max(10, Math.min(90, prev.cpuUsage + (Math.random() - 0.5) * 10)),
        memoryUsage: Math.max(20, Math.min(95, prev.memoryUsage + (Math.random() - 0.5) * 5)),
        networkSpeed: `${(Math.random() * 5 + 0.5).toFixed(1)} MB/s`,
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, [fetchTasks]);

  const getTaskStats = () => {
    const stats = {
      total: tasks.length,
      running: tasks.filter(t => t.status === 'running').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      failed: tasks.filter(t => t.status === 'failed').length,
      pending: tasks.filter(t => t.status === 'pending').length,
      download: tasks.filter(t => t.type === 'download').length,
      forward: tasks.filter(t => t.type === 'forward').length,
      listen: tasks.filter(t => t.type === 'listen_forward').length,
    };
    return stats;
  };

  const stats = getTaskStats();

  const handleTaskAction = async (action: string, taskId: string) => {
    try {
      switch (action) {
        case 'start':
          await startTask(taskId);
          break;
        case 'pause':
          await pauseTask(taskId);
          break;
        case 'stop':
          await stopTask(taskId);
          break;
        case 'delete':
          await deleteTask(taskId);
          break;
      }
    } catch (error) {
      console.error(`Task action ${action} failed:`, error);
    }
  };

  const recentTasks = tasks.slice(-5).reverse();

  const systemStatusColumns = [
    {
      title: '指标',
      dataIndex: 'metric',
      key: 'metric',
    },
    {
      title: '当前值',
      dataIndex: 'value',
      key: 'value',
      render: (value: any, record: any) => {
        if (record.type === 'progress') {
          return <Progress percent={value} size="small" />;
        }
        return value;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colorMap = {
          good: 'green',
          warning: 'orange',
          error: 'red',
        };
        return <Badge status={status as any} text={status === 'good' ? '正常' : status === 'warning' ? '警告' : '错误'} />;
      },
    },
  ];

  const systemStatusData = [
    {
      key: 'cpu',
      metric: 'CPU使用率',
      value: systemStatus.cpuUsage,
      type: 'progress',
      status: systemStatus.cpuUsage > 80 ? 'error' : systemStatus.cpuUsage > 60 ? 'warning' : 'good',
    },
    {
      key: 'memory',
      metric: '内存使用率',
      value: systemStatus.memoryUsage,
      type: 'progress',
      status: systemStatus.memoryUsage > 90 ? 'error' : systemStatus.memoryUsage > 75 ? 'warning' : 'good',
    },
    {
      key: 'disk',
      metric: '磁盘使用率',
      value: systemStatus.diskUsage,
      type: 'progress',
      status: systemStatus.diskUsage > 90 ? 'error' : systemStatus.diskUsage > 80 ? 'warning' : 'good',
    },
    {
      key: 'network',
      metric: '网络速度',
      value: systemStatus.networkSpeed,
      type: 'text',
      status: 'good',
    },
  ];

  return (
    <div>
      <Title level={2}>
        <DashboardOutlined /> 仪表板
      </Title>

      {/* 连接状态提示 */}
      {!isConnected && (
        <Alert
          message="WebSocket连接已断开"
          description="实时数据更新可能受到影响，请检查网络连接。"
          type="warning"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 任务统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总任务数"
              value={stats.total}
              prefix={<DashboardOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="运行中"
              value={stats.running}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completed}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="失败/错误"
              value={stats.failed}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 任务类型统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="下载任务"
              value={stats.download}
              prefix={<DownloadOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="转发任务"
              value={stats.forward}
              prefix={<ShareAltOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="监听任务"
              value={stats.listen}
              prefix={<SoundOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 活跃任务 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <ThunderboltOutlined />
                活跃任务
                <Badge count={activeTasks.length} />
              </Space>
            }
            extra={
              <Button type="link" size="small">
                查看全部
              </Button>
            }
          >
            <List
              loading={isLoading}
              dataSource={activeTasks.slice(0, 3)}
              renderItem={(task) => (
                <List.Item style={{ padding: '8px 0' }}>
                  <TaskCard
                    task={task}
                    onStart={(id) => handleTaskAction('start', id)}
                    onPause={(id) => handleTaskAction('pause', id)}
                    onStop={(id) => handleTaskAction('stop', id)}
                    onDelete={(id) => handleTaskAction('delete', id)}
                  />
                </List.Item>
              )}
              locale={{ emptyText: '暂无活跃任务' }}
            />
          </Card>
        </Col>

        {/* 系统状态 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <CloudServerOutlined />
                系统状态
                <Badge status={isConnected ? 'processing' : 'error'} />
              </Space>
            }
            extra={
              <Tooltip title={`WebSocket: ${isConnected ? '已连接' : '未连接'}`}>
                <WifiOutlined style={{ color: isConnected ? '#52c41a' : '#ff4d4f' }} />
              </Tooltip>
            }
          >
            <Table
              dataSource={systemStatusData}
              columns={systemStatusColumns}
              pagination={false}
              size="small"
            />
            <div style={{ marginTop: 16 }}>
              <Text type="secondary">
                系统运行时间: {systemStatus.uptime}
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 最近任务 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={24}>
          <Card
            title={
              <Space>
                <ClockCircleOutlined />
                最近任务
              </Space>
            }
          >
            <List
              loading={isLoading}
              dataSource={recentTasks}
              renderItem={(task) => (
                <List.Item
                  actions={[
                    <Tag key="status" color={task.status === 'completed' ? 'green' : task.status === 'failed' ? 'red' : 'blue'}>
                      {task.status}
                    </Tag>,
                    <Text key="time" type="secondary">
                      {new Date(task.updatedAt).toLocaleString()}
                    </Text>,
                  ]}
                >
                  <List.Item.Meta
                    title={`${task.type} 任务`}
                    description={`ID: ${task.id.slice(0, 8)}... | 进度: ${task.progress}%`}
                  />
                </List.Item>
              )}
              locale={{ emptyText: '暂无任务记录' }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
