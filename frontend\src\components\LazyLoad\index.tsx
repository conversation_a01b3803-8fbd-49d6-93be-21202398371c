import React, { Suspense } from 'react';
import { Spin } from 'antd';

interface LazyLoadProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  minHeight?: number;
}

const LazyLoad: React.FC<LazyLoadProps> = ({ 
  children, 
  fallback,
  minHeight = 200 
}) => {
  const defaultFallback = (
    <div 
      style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: minHeight,
        width: '100%' 
      }}
    >
      <Spin size="large" tip="加载中..." />
    </div>
  );

  return (
    <Suspense fallback={fallback || defaultFallback}>
      {children}
    </Suspense>
  );
};

export default LazyLoad;
