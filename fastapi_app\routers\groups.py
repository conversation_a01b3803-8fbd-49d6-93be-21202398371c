"""
群组管理API路由
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..auth import get_current_active_user, User
from ..database import get_db, TelegramGroup
from ..utils import create_response

router = APIRouter()


class GroupResponse(BaseModel):
    """群组响应模型"""
    id: str
    title: str
    username: Optional[str] = None
    type: str
    memberCount: Optional[int] = None
    description: Optional[str] = None


@router.get("")
async def get_groups(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取群组列表"""
    try:
        # 查询所有活跃群组
        result = await db.execute(
            select(TelegramGroup).where(TelegramGroup.is_active == True)
        )
        groups = result.scalars().all()
        
        # 转换为响应格式
        group_list = []
        for group in groups:
            group_data = GroupResponse(
                id=group.group_id,
                title=group.title,
                username=group.username,
                type=group.type,
                memberCount=group.member_count,
                description=group.description
            )
            group_list.append(group_data.dict())
        
        return create_response(
            success=True,
            data=group_list
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取群组列表失败: {str(e)}"
        )


@router.get("/{group_id}")
async def get_group(
    group_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取单个群组信息"""
    try:
        result = await db.execute(
            select(TelegramGroup).where(TelegramGroup.group_id == group_id)
        )
        group = result.scalar_one_or_none()
        
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="群组不存在"
            )
        
        group_response = GroupResponse(
            id=group.group_id,
            title=group.title,
            username=group.username,
            type=group.type,
            memberCount=group.member_count,
            description=group.description
        )
        
        return create_response(
            success=True,
            data=group_response.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取群组信息失败: {str(e)}"
        )
