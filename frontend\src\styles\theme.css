/* 主题相关的全局样式 */

:root {
  /* 默认亮色主题变量 */
  --ant-color-primary: #1890ff;
  --ant-color-bg-container: #ffffff;
  --ant-color-bg-layout: #f5f5f5;
  --ant-color-text: rgba(0, 0, 0, 0.88);
  --ant-color-text-secondary: rgba(0, 0, 0, 0.65);
  --ant-color-border: #d9d9d9;
  --ant-color-bg-elevated: #ffffff;
}

/* 主题切换过渡动画 */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* 亮色主题 */
[data-theme="light"] {
  --ant-color-bg-container: #ffffff;
  --ant-color-bg-layout: #f5f5f5;
  --ant-color-text: rgba(0, 0, 0, 0.88);
  --ant-color-text-secondary: rgba(0, 0, 0, 0.65);
  --ant-color-border: #d9d9d9;
  --ant-color-bg-elevated: #ffffff;
}

/* 暗色主题 */
[data-theme="dark"] {
  --ant-color-bg-container: #141414;
  --ant-color-bg-layout: #000000;
  --ant-color-text: rgba(255, 255, 255, 0.88);
  --ant-color-text-secondary: rgba(255, 255, 255, 0.65);
  --ant-color-border: #424242;
  --ant-color-bg-elevated: #1f1f1f;
}

/* 确保所有元素都使用主题变量 */
.ant-layout-header {
  background: var(--ant-color-bg-container) !important;
  border-bottom: 1px solid var(--ant-color-border) !important;
}

.ant-layout-content {
  background: var(--ant-color-bg-container) !important;
}

.ant-dropdown-menu {
  background: var(--ant-color-bg-elevated) !important;
  border: 1px solid var(--ant-color-border) !important;
}

.ant-dropdown-menu-item {
  color: var(--ant-color-text) !important;
}

.ant-dropdown-menu-item:hover {
  background: var(--ant-color-bg-container) !important;
}

/* 确保body和html的主题应用 */
html, body {
  background-color: var(--ant-color-bg-layout);
  color: var(--ant-color-text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 滚动条主题 */
[data-theme="light"] ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

[data-theme="light"] ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

[data-theme="light"] ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

[data-theme="light"] ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

[data-theme="dark"] ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: #2f2f2f;
  border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* 选择文本的颜色 */
[data-theme="light"] ::selection {
  background-color: rgba(24, 144, 255, 0.2);
  color: var(--ant-color-text);
}

[data-theme="dark"] ::selection {
  background-color: rgba(24, 144, 255, 0.3);
  color: var(--ant-color-text);
}

/* 焦点样式 */
[data-theme="light"] *:focus-visible {
  outline: 2px solid var(--ant-color-primary);
  outline-offset: 2px;
}

[data-theme="dark"] *:focus-visible {
  outline: 2px solid var(--ant-color-primary);
  outline-offset: 2px;
}

/* 确保模态框背景正确 */
.ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.45);
}

[data-theme="dark"] .ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.65);
}

/* 抽屉组件主题 */
[data-theme="dark"] .ant-drawer-content {
  background-color: var(--ant-color-bg-container);
}

/* 确保卡片组件主题正确 */
[data-theme="dark"] .ant-card {
  background-color: var(--ant-color-bg-container);
  border-color: var(--ant-color-border);
}

/* 表格组件主题 */
[data-theme="dark"] .ant-table {
  background-color: var(--ant-color-bg-container);
}

[data-theme="dark"] .ant-table-thead > tr > th {
  background-color: var(--ant-color-bg-elevated);
  border-color: var(--ant-color-border);
}

/* 输入框组件主题 */
[data-theme="dark"] .ant-input,
[data-theme="dark"] .ant-input-number,
[data-theme="dark"] .ant-select-selector {
  background-color: var(--ant-color-bg-container);
  border-color: var(--ant-color-border);
  color: var(--ant-color-text);
}

/* 下拉菜单主题 */
[data-theme="dark"] .ant-dropdown {
  background-color: var(--ant-color-bg-elevated);
}

/* 工具提示主题 */
[data-theme="dark"] .ant-tooltip-inner {
  background-color: var(--ant-color-bg-elevated);
  color: var(--ant-color-text);
}

/* 消息提示主题 */
[data-theme="dark"] .ant-message-notice-content {
  background-color: var(--ant-color-bg-elevated);
  color: var(--ant-color-text);
}

/* 通知主题 */
[data-theme="dark"] .ant-notification-notice {
  background-color: var(--ant-color-bg-elevated);
  border-color: var(--ant-color-border);
}

/* 加载动画主题 */
[data-theme="dark"] .ant-spin-dot-item {
  background-color: var(--ant-color-primary);
}

/* 登录页面特定样式 */
[data-theme="dark"] .ant-layout-content {
  background: var(--ant-color-bg-layout) !important;
}

/* 确保登录表单在暗色模式下正确显示 */
[data-theme="dark"] .ant-form-item-label > label {
  color: var(--ant-color-text);
}

[data-theme="dark"] .ant-form-item-explain-error {
  color: var(--ant-color-error);
}

/* 登录页面卡片样式 */
[data-theme="dark"] .ant-card-head {
  background-color: var(--ant-color-bg-elevated);
  border-color: var(--ant-color-border);
}

[data-theme="dark"] .ant-card-body {
  background-color: var(--ant-color-bg-container);
}

/* 分页组件主题 */
[data-theme="dark"] .ant-pagination-item {
  background-color: var(--ant-color-bg-container);
  border-color: var(--ant-color-border);
}

[data-theme="dark"] .ant-pagination-item a {
  color: var(--ant-color-text);
}

/* 标签页主题 */
[data-theme="dark"] .ant-tabs-content-holder {
  background-color: var(--ant-color-bg-container);
}

/* 步骤条主题 */
[data-theme="dark"] .ant-steps-item-process .ant-steps-item-icon {
  background-color: var(--ant-color-primary);
}

/* 时间轴主题 */
[data-theme="dark"] .ant-timeline-item-tail {
  border-color: var(--ant-color-border);
}

/* 树形控件主题 */
[data-theme="dark"] .ant-tree {
  background-color: var(--ant-color-bg-container);
}

/* 穿梭框主题 */
[data-theme="dark"] .ant-transfer-list {
  background-color: var(--ant-color-bg-container);
  border-color: var(--ant-color-border);
}

/* 上传组件主题 */
[data-theme="dark"] .ant-upload-drag {
  background-color: var(--ant-color-bg-container);
  border-color: var(--ant-color-border);
}

/* 日期选择器主题 */
[data-theme="dark"] .ant-picker-dropdown {
  background-color: var(--ant-color-bg-elevated);
}

/* 颜色选择器主题 */
[data-theme="dark"] .ant-color-picker-panel {
  background-color: var(--ant-color-bg-elevated);
}

/* 确保所有弹出层都有正确的主题 */
[data-theme="dark"] .ant-popover-inner {
  background-color: var(--ant-color-bg-elevated);
  color: var(--ant-color-text);
}

/* 主题切换动画类 */
.theme-transition * {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease !important;
}

/* 跳过链接样式 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
  border-radius: 4px;
}

.skip-link:focus {
  top: 6px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  [data-theme="light"] {
    --ant-color-text: #000000;
    --ant-color-border: #000000;
  }
  
  [data-theme="dark"] {
    --ant-color-text: #ffffff;
    --ant-color-border: #ffffff;
  }
}

