import { test, expect } from '@playwright/test';

test.describe('Navigation', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/login');
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'admin');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('should navigate between pages using sidebar menu', async ({ page }) => {
    // 测试导航到下载页面
    await page.click('text=下载管理');
    await expect(page).toHaveURL('/download');
    await expect(page.locator('h2')).toContainText('下载管理');

    // 测试导航到转发页面
    await page.click('text=转发管理');
    await expect(page).toHaveURL('/forward');
    await expect(page.locator('h2')).toContainText('转发管理');

    // 测试导航到监听页面
    await page.click('text=监听转发');
    await expect(page).toHaveURL('/listen');
    await expect(page.locator('h2')).toContainText('监听转发');

    // 测试导航到设置页面
    await page.click('text=系统设置');
    await expect(page).toHaveURL('/settings');
    await expect(page.locator('h2')).toContainText('系统设置');

    // 测试导航回仪表板
    await page.click('text=仪表板');
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('h2')).toContainText('仪表板');
  });

  test('should highlight active menu item', async ({ page }) => {
    // 检查仪表板菜单项是否高亮
    const dashboardMenuItem = page.locator('.ant-menu-item-selected').filter({ hasText: '仪表板' });
    await expect(dashboardMenuItem).toBeVisible();

    // 导航到下载页面并检查高亮
    await page.click('text=下载管理');
    const downloadMenuItem = page.locator('.ant-menu-item-selected').filter({ hasText: '下载管理' });
    await expect(downloadMenuItem).toBeVisible();
  });

  test('should collapse and expand sidebar', async ({ page }) => {
    // 点击折叠按钮
    await page.click('button[aria-label*="fold"], button[aria-label*="unfold"]');
    
    // 检查侧边栏是否折叠（通过检查宽度或类名）
    const sider = page.locator('.ant-layout-sider');
    await expect(sider).toHaveClass(/ant-layout-sider-collapsed/);

    // 再次点击展开
    await page.click('button[aria-label*="fold"], button[aria-label*="unfold"]');
    
    // 检查侧边栏是否展开
    await expect(sider).not.toHaveClass(/ant-layout-sider-collapsed/);
  });

  test('should work on mobile devices', async ({ page }) => {
    // 设置移动设备视口
    await page.setViewportSize({ width: 375, height: 667 });
    
    // 在移动设备上，侧边栏应该是隐藏的
    const sider = page.locator('.ant-layout-sider');
    await expect(sider).not.toBeVisible();
    
    // 点击菜单按钮应该打开抽屉
    await page.click('button[aria-label*="fold"], button[aria-label*="unfold"]');
    
    // 检查抽屉是否打开
    const drawer = page.locator('.ant-drawer');
    await expect(drawer).toBeVisible();
    
    // 点击导航项应该关闭抽屉
    await page.click('text=下载管理');
    await expect(drawer).not.toBeVisible();
    await expect(page).toHaveURL('/download');
  });

  test('should handle direct URL access', async ({ page }) => {
    // 直接访问下载页面
    await page.goto('/download');
    await expect(page).toHaveURL('/download');
    await expect(page.locator('h2')).toContainText('下载管理');
    
    // 检查菜单项是否正确高亮
    const downloadMenuItem = page.locator('.ant-menu-item-selected').filter({ hasText: '下载管理' });
    await expect(downloadMenuItem).toBeVisible();
  });

  test('should handle invalid routes', async ({ page }) => {
    // 访问不存在的路由
    await page.goto('/nonexistent');
    
    // 应该重定向到仪表板
    await expect(page).toHaveURL('/dashboard');
  });
});
