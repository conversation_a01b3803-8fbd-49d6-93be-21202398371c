import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Form,
  Button,
  Space,
  Typography,
  message,
  List,
  Statistic,
  Badge,
} from 'antd';
import { PlusOutlined, SoundOutlined, PlayCircleOutlined, PauseCircleOutlined } from '@ant-design/icons';
import AccountSelector from '@/components/AccountSelector';
import GroupSelector from '@/components/GroupSelector';
import TaskCard from '@/components/TaskCard';
import ForwardRules from '@/components/ForwardRules';
import MessageStream from '@/components/MessageStream';
import { useTaskStore } from '@/store/taskStore';
import { useWebSocket } from '@/hooks/useWebSocket';
import type { Account, Group, ListenForwardTask, TaskType, ForwardRule } from '@/types';

const { Title } = Typography;

interface Message {
  id: string;
  content: string;
  sender: string;
  timestamp: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'document';
  forwarded: boolean;
  groupName: string;
}

const Listen: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [groups, setGroups] = useState<Group[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<string>();
  const [forwardRules, setForwardRules] = useState<ForwardRule[]>([]);
  const [isListening, setIsListening] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [stats, setStats] = useState({
    totalMessages: 0,
    forwardedMessages: 0,
    activeListeners: 0,
  });

  const {
    tasks,
    isLoading: tasksLoading,
    createTask,
    startTask,
    pauseTask,
    stopTask,
    deleteTask,
    fetchTasks,
    getTasksByType,
  } = useTaskStore();

  const { isConnected, sendMessage } = useWebSocket();
  const listenTasks = getTasksByType('listen_forward' as TaskType);

  // 模拟数据
  useEffect(() => {
    // 模拟账号数据
    setAccounts([
      {
        id: '1',
        name: '主账号',
        phone: '+86 138****1234',
        isActive: true,
        lastLogin: '2024-01-15 10:30:00',
      },
      {
        id: '2',
        name: '备用账号',
        phone: '+86 139****5678',
        isActive: true,
        lastLogin: '2024-01-14 15:20:00',
      },
    ]);

    // 获取任务列表
    fetchTasks();

    // 模拟转发规则
    setForwardRules([
      {
        id: '1',
        name: '重要消息转发',
        sourcePattern: '.*(重要|紧急|通知).*',
        targetPattern: '🚨 [重要] $1',
        isActive: true,
      },
      {
        id: '2',
        name: '图片自动转发',
        sourcePattern: '.*\\.(jpg|png|gif|jpeg)$',
        targetPattern: '📸 [图片分享] $1',
        isActive: true,
      },
    ]);

    // 模拟统计数据
    setStats({
      totalMessages: 1247,
      forwardedMessages: 89,
      activeListeners: 3,
    });
  }, [fetchTasks]);

  // 当选择账号时，获取该账号的群组列表
  useEffect(() => {
    if (selectedAccount) {
      // 模拟群组数据
      setGroups([
        {
          id: 'group1',
          title: '技术交流群',
          type: 'supergroup',
          memberCount: 1250,
          description: '技术讨论和资源分享',
        },
        {
          id: 'channel1',
          title: '资源分享频道',
          type: 'channel',
          memberCount: 5680,
          description: '各种学习资源和工具分享',
        },
        {
          id: 'group2',
          title: '项目协作群',
          type: 'group',
          memberCount: 45,
          description: '项目开发协作讨论',
        },
        {
          id: 'channel2',
          title: '通知频道',
          type: 'channel',
          memberCount: 2340,
          description: '重要通知和公告',
        },
      ]);
    } else {
      setGroups([]);
    }
  }, [selectedAccount]);

  // 模拟实时消息
  useEffect(() => {
    if (isListening) {
      const interval = setInterval(() => {
        const newMessage: Message = {
          id: Date.now().toString(),
          content: `这是一条模拟消息 ${new Date().toLocaleTimeString()}`,
          sender: `用户${Math.floor(Math.random() * 100)}`,
          timestamp: new Date().toISOString(),
          type: ['text', 'image', 'video', 'audio', 'document'][Math.floor(Math.random() * 5)] as any,
          forwarded: Math.random() > 0.7,
          groupName: '技术交流群',
        };

        setMessages(prev => [...prev, newMessage]);
        setStats(prev => ({
          ...prev,
          totalMessages: prev.totalMessages + 1,
          forwardedMessages: newMessage.forwarded ? prev.forwardedMessages + 1 : prev.forwardedMessages,
        }));
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [isListening]);

  const handleCreateTask = async (values: any) => {
    setLoading(true);
    try {
      const taskData: Partial<ListenForwardTask> = {
        type: 'listen_forward' as TaskType,
        accountId: values.accountId,
        sourceGroupId: values.sourceGroupId,
        targetGroupId: values.targetGroupId,
        forwardRules: forwardRules.filter(rule => rule.isActive),
        isListening: false,
        progress: 0,
        totalFiles: 0,
        completedFiles: 0,
        failedFiles: 0,
      };

      await createTask(taskData);
      message.success('监听转发任务创建成功！');
      form.resetFields();
    } catch (error) {
      message.error('创建任务失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTaskAction = async (action: string, taskId: string) => {
    try {
      switch (action) {
        case 'start':
          await startTask(taskId);
          message.success('任务已开始');
          break;
        case 'pause':
          await pauseTask(taskId);
          message.success('任务已暂停');
          break;
        case 'stop':
          await stopTask(taskId);
          message.success('任务已停止');
          break;
        case 'delete':
          await deleteTask(taskId);
          message.success('任务已删除');
          break;
      }
    } catch (error) {
      message.error(`操作失败: ${error}`);
    }
  };

  const handleToggleListening = () => {
    setIsListening(!isListening);
    if (!isListening) {
      message.success('开始监听消息');
      setStats(prev => ({ ...prev, activeListeners: prev.activeListeners + 1 }));
    } else {
      message.info('停止监听消息');
      setStats(prev => ({ ...prev, activeListeners: Math.max(0, prev.activeListeners - 1) }));
    }
  };

  return (
    <div>
      <Title level={2}>
        <SoundOutlined /> 监听转发
      </Title>

      {/* 统计信息 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={8}>
          <Card size="small">
            <Statistic
              title="总消息数"
              value={stats.totalMessages}
              prefix={<Badge status="processing" />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card size="small">
            <Statistic
              title="已转发"
              value={stats.forwardedMessages}
              prefix={<Badge status="success" />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card size="small">
            <Statistic
              title="活跃监听"
              value={stats.activeListeners}
              prefix={<Badge status={stats.activeListeners > 0 ? 'processing' : 'default'} />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 左侧：创建任务和规则管理 */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            {/* 创建监听任务 */}
            <Card title="创建监听任务" size="small">
              <Form
                form={form}
                layout="vertical"
                onFinish={handleCreateTask}
              >
                <Form.Item
                  name="accountId"
                  label="选择账号"
                  rules={[{ required: true, message: '请选择账号' }]}
                >
                  <AccountSelector
                    accounts={accounts}
                    onChange={setSelectedAccount}
                    placeholder="选择要使用的Telegram账号"
                  />
                </Form.Item>

                <Form.Item
                  name="sourceGroupId"
                  label="监听群组"
                  rules={[{ required: true, message: '请选择监听群组' }]}
                >
                  <GroupSelector
                    groups={groups}
                    placeholder="选择要监听的群组"
                    multiple
                  />
                </Form.Item>

                <Form.Item
                  name="targetGroupId"
                  label="转发目标"
                  rules={[{ required: true, message: '请选择转发目标' }]}
                >
                  <GroupSelector
                    groups={groups}
                    placeholder="选择转发目标群组"
                    multiple
                  />
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<PlusOutlined />}
                    block
                  >
                    创建监听任务
                  </Button>
                </Form.Item>
              </Form>
            </Card>

            {/* 转发规则 */}
            <ForwardRules
              rules={forwardRules}
              onChange={setForwardRules}
            />
          </Space>
        </Col>

        {/* 中间：实时消息流 */}
        <Col xs={24} lg={8}>
          <MessageStream
            isListening={isListening}
            onToggleListening={handleToggleListening}
            messages={messages}
          />
        </Col>

        {/* 右侧：任务列表 */}
        <Col xs={24} lg={8}>
          <Card title="监听任务列表" size="small">
            <List
              loading={tasksLoading}
              dataSource={listenTasks}
              renderItem={(task) => (
                <List.Item style={{ padding: '8px 0' }}>
                  <TaskCard
                    task={task}
                    onStart={(id) => handleTaskAction('start', id)}
                    onPause={(id) => handleTaskAction('pause', id)}
                    onStop={(id) => handleTaskAction('stop', id)}
                    onDelete={(id) => handleTaskAction('delete', id)}
                  />
                </List.Item>
              )}
              locale={{ emptyText: '暂无监听任务' }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Listen;
