# API 参考文档

## 基础信息

- **Base URL**: `http://localhost:5000/api`
- **API Version**: v1
- **Content-Type**: `application/json`
- **Authentication**: Session-based

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {...},
  "message": "操作成功",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 认证接口

### 登录
```http
POST /api/auth/login
```

**请求体**:
```json
{
  "username": "admin",
  "password": "admin"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "1",
      "username": "admin",
      "role": "admin"
    },
    "token": "session_token"
  }
}
```

### 登出
```http
POST /api/auth/logout
```

### 检查认证状态
```http
GET /api/auth/status
```

## 账号管理

### 获取账号列表
```http
GET /api/accounts
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "account_1",
      "name": "主账号",
      "phone": "+86 138****1234",
      "isActive": true,
      "lastLogin": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### 添加账号
```http
POST /api/accounts
```

**请求体**:
```json
{
  "name": "新账号",
  "phone": "+86 ***********"
}
```

### 更新账号
```http
PUT /api/accounts/{accountId}
```

### 删除账号
```http
DELETE /api/accounts/{accountId}
```

## 群组管理

### 获取群组列表
```http
GET /api/groups?accountId={accountId}
```

**查询参数**:
- `accountId` (required): 账号ID
- `type` (optional): 群组类型 (group, channel, supergroup)
- `search` (optional): 搜索关键词

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "group_1",
      "title": "技术交流群",
      "type": "supergroup",
      "memberCount": 1000,
      "description": "技术讨论和分享"
    }
  ]
}
```

### 获取群组详情
```http
GET /api/groups/{groupId}
```

## 任务管理

### 获取任务列表
```http
GET /api/tasks
```

**查询参数**:
- `type` (optional): 任务类型 (download, forward, listen_forward)
- `status` (optional): 任务状态 (pending, running, completed, failed, paused)
- `page` (optional): 页码，默认1
- `limit` (optional): 每页数量，默认20

**响应**:
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "id": "task_1",
        "type": "download",
        "status": "running",
        "accountId": "account_1",
        "sourceGroupId": "group_1",
        "targetGroupId": null,
        "startMessageId": 1,
        "endMessageId": 100,
        "progress": 50,
        "totalFiles": 10,
        "completedFiles": 5,
        "failedFiles": 0,
        "downloadSpeed": "1.5 MB/s",
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T10:35:00Z",
        "error": null
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "pages": 3
    }
  }
}
```

### 创建任务
```http
POST /api/tasks
```

**请求体**:
```json
{
  "type": "download",
  "accountId": "account_1",
  "sourceGroupId": "group_1",
  "startMessageId": 1,
  "endMessageId": 100,
  "fileTypes": ["image", "video"],
  "downloadPath": "./downloads",
  "filter": "重要文件"
}
```

### 获取任务详情
```http
GET /api/tasks/{taskId}
```

### 更新任务
```http
PUT /api/tasks/{taskId}
```

**请求体**:
```json
{
  "status": "paused"
}
```

### 删除任务
```http
DELETE /api/tasks/{taskId}
```

### 任务操作

#### 开始任务
```http
POST /api/tasks/{taskId}/start
```

#### 暂停任务
```http
POST /api/tasks/{taskId}/pause
```

#### 停止任务
```http
POST /api/tasks/{taskId}/stop
```

## 转发规则

### 获取规则列表
```http
GET /api/forward-rules
```

### 创建规则
```http
POST /api/forward-rules
```

**请求体**:
```json
{
  "name": "图片转发规则",
  "sourcePattern": ".*\\.(jpg|png|gif)$",
  "targetTemplate": "转发图片: {content}",
  "enabled": true
}
```

### 更新规则
```http
PUT /api/forward-rules/{ruleId}
```

### 删除规则
```http
DELETE /api/forward-rules/{ruleId}
```

## 系统设置

### 获取设置
```http
GET /api/settings
```

**响应**:
```json
{
  "success": true,
  "data": {
    "downloadPath": "./downloads",
    "maxConcurrentTasks": 3,
    "theme": "light",
    "language": "zh-CN",
    "proxySettings": {
      "enabled": false,
      "type": "http",
      "host": "",
      "port": 0,
      "username": "",
      "password": ""
    },
    "notificationSettings": {
      "enabled": true,
      "taskCompletion": true,
      "taskFailure": true,
      "systemAlerts": true
    }
  }
}
```

### 更新设置
```http
PUT /api/settings
```

**请求体**:
```json
{
  "downloadPath": "./downloads",
  "maxConcurrentTasks": 5,
  "theme": "dark"
}
```

## 统计信息

### 获取仪表板数据
```http
GET /api/dashboard
```

**响应**:
```json
{
  "success": true,
  "data": {
    "taskStats": {
      "total": 100,
      "running": 5,
      "completed": 80,
      "failed": 10,
      "pending": 5
    },
    "downloadStats": {
      "totalFiles": 1000,
      "totalSize": "10.5 GB",
      "todayFiles": 50,
      "todaySize": "500 MB"
    },
    "systemStats": {
      "uptime": "5 days, 10 hours",
      "memoryUsage": "2.1 GB",
      "diskUsage": "45%",
      "activeConnections": 3
    },
    "recentTasks": [...],
    "chartData": {
      "dailyDownloads": [...],
      "taskStatusDistribution": [...],
      "fileTypeDistribution": [...]
    }
  }
}
```

### 获取系统状态
```http
GET /api/system/status
```

### 获取系统日志
```http
GET /api/system/logs
```

**查询参数**:
- `level` (optional): 日志级别 (debug, info, warning, error)
- `limit` (optional): 返回条数，默认100
- `since` (optional): 起始时间

## WebSocket 事件

### 连接
```javascript
const ws = new WebSocket('ws://localhost:5000/ws');
```

### 事件类型

#### 任务进度更新
```json
{
  "type": "task_progress",
  "data": {
    "taskId": "task_1",
    "progress": 75,
    "completedFiles": 7,
    "totalFiles": 10,
    "downloadSpeed": "2.1 MB/s"
  }
}
```

#### 任务状态更新
```json
{
  "type": "task_update",
  "data": {
    "taskId": "task_1",
    "status": "completed",
    "updatedAt": "2024-01-15T10:45:00Z"
  }
}
```

#### 系统状态更新
```json
{
  "type": "system_status",
  "data": {
    "memoryUsage": "2.3 GB",
    "activeConnections": 4,
    "runningTasks": 3
  }
}
```

#### 错误通知
```json
{
  "type": "error",
  "data": {
    "message": "任务执行失败",
    "taskId": "task_1",
    "error": "网络连接超时"
  }
}
```

## 错误代码

| 代码 | 描述 |
|------|------|
| AUTH_REQUIRED | 需要认证 |
| INVALID_CREDENTIALS | 无效的登录凭据 |
| ACCOUNT_NOT_FOUND | 账号不存在 |
| GROUP_NOT_FOUND | 群组不存在 |
| TASK_NOT_FOUND | 任务不存在 |
| INVALID_PARAMETERS | 无效的参数 |
| OPERATION_FAILED | 操作失败 |
| RATE_LIMIT_EXCEEDED | 请求频率超限 |
| INTERNAL_ERROR | 内部服务器错误 |

## 限制说明

- API请求频率限制: 100次/分钟
- 文件上传大小限制: 100MB
- 并发任务数量限制: 10个
- WebSocket连接数限制: 50个

## 示例代码

### JavaScript/TypeScript
```typescript
// 创建下载任务
const createDownloadTask = async (taskData) => {
  const response = await fetch('/api/tasks', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(taskData),
  });
  
  const result = await response.json();
  return result;
};

// WebSocket连接
const connectWebSocket = () => {
  const ws = new WebSocket('ws://localhost:5000/ws');
  
  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
  };
  
  return ws;
};
```

### Python
```python
import requests

# 登录
def login(username, password):
    response = requests.post('http://localhost:5000/api/auth/login', json={
        'username': username,
        'password': password
    })
    return response.json()

# 获取任务列表
def get_tasks():
    response = requests.get('http://localhost:5000/api/tasks')
    return response.json()
```
