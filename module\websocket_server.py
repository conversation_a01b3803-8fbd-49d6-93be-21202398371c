"""
WebSocket服务器模块 - 提供实时通信功能
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room, disconnect
from flask import session, request

logger = logging.getLogger(__name__)

class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.connected_clients: Dict[str, Dict[str, Any]] = {}
        self.user_rooms: Dict[str, List[str]] = {}
    
    def add_client(self, sid: str, user_id: str, username: str):
        """添加客户端连接"""
        self.connected_clients[sid] = {
            'user_id': user_id,
            'username': username,
            'connected_at': datetime.utcnow().isoformat(),
            'last_activity': datetime.utcnow().isoformat(),
        }
        
        # 将用户加入个人房间
        if user_id not in self.user_rooms:
            self.user_rooms[user_id] = []
        self.user_rooms[user_id].append(sid)
        
        logger.info(f"Client {sid} connected for user {username}")
    
    def remove_client(self, sid: str):
        """移除客户端连接"""
        if sid in self.connected_clients:
            client_info = self.connected_clients[sid]
            user_id = client_info['user_id']
            username = client_info['username']
            
            # 从用户房间移除
            if user_id in self.user_rooms:
                self.user_rooms[user_id] = [s for s in self.user_rooms[user_id] if s != sid]
                if not self.user_rooms[user_id]:
                    del self.user_rooms[user_id]
            
            del self.connected_clients[sid]
            logger.info(f"Client {sid} disconnected for user {username}")
    
    def update_activity(self, sid: str):
        """更新客户端活动时间"""
        if sid in self.connected_clients:
            self.connected_clients[sid]['last_activity'] = datetime.utcnow().isoformat()
    
    def get_client_info(self, sid: str) -> Optional[Dict[str, Any]]:
        """获取客户端信息"""
        return self.connected_clients.get(sid)
    
    def get_user_sessions(self, user_id: str) -> List[str]:
        """获取用户的所有会话"""
        return self.user_rooms.get(user_id, [])
    
    def get_connected_users(self) -> List[Dict[str, Any]]:
        """获取所有连接的用户"""
        users = {}
        for client_info in self.connected_clients.values():
            user_id = client_info['user_id']
            if user_id not in users:
                users[user_id] = {
                    'user_id': user_id,
                    'username': client_info['username'],
                    'session_count': 0,
                    'first_connected': client_info['connected_at'],
                    'last_activity': client_info['last_activity'],
                }
            users[user_id]['session_count'] += 1
            if client_info['last_activity'] > users[user_id]['last_activity']:
                users[user_id]['last_activity'] = client_info['last_activity']
        
        return list(users.values())

# 全局WebSocket管理器
ws_manager = WebSocketManager()

def init_websocket(socketio: SocketIO):
    """初始化WebSocket事件处理"""
    
    @socketio.on('connect')
    def handle_connect():
        """处理客户端连接"""
        try:
            # 检查认证状态
            if 'user_id' not in session:
                logger.warning(f"Unauthenticated connection attempt from {request.remote_addr}")
                disconnect()
                return False
            
            user_id = session['user_id']
            username = session.get('username', 'Unknown')
            sid = request.sid
            
            # 添加客户端
            ws_manager.add_client(sid, user_id, username)
            
            # 加入用户房间
            join_room(f"user_{user_id}")
            
            # 发送连接成功消息
            emit('connected', {
                'message': 'Connected successfully',
                'user_id': user_id,
                'username': username,
                'timestamp': datetime.utcnow().isoformat(),
            })
            
            logger.info(f"User {username} connected via WebSocket")
            
        except Exception as e:
            logger.error(f"Connection error: {e}")
            disconnect()
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """处理客户端断开连接"""
        try:
            sid = request.sid
            client_info = ws_manager.get_client_info(sid)
            
            if client_info:
                user_id = client_info['user_id']
                username = client_info['username']
                
                # 离开用户房间
                leave_room(f"user_{user_id}")
                
                # 移除客户端
                ws_manager.remove_client(sid)
                
                logger.info(f"User {username} disconnected from WebSocket")
            
        except Exception as e:
            logger.error(f"Disconnect error: {e}")
    
    @socketio.on('ping')
    def handle_ping():
        """处理心跳包"""
        try:
            sid = request.sid
            ws_manager.update_activity(sid)
            emit('pong', {'timestamp': datetime.utcnow().isoformat()})
        except Exception as e:
            logger.error(f"Ping error: {e}")

def broadcast_task_update(socketio: SocketIO, task_data: Dict[str, Any]):
    """广播任务更新"""
    try:
        socketio.emit('task_update', {
            'type': 'task_update',
            'data': task_data,
            'timestamp': datetime.utcnow().isoformat(),
        })
        logger.debug(f"Broadcasted task update for task {task_data.get('id')}")
    except Exception as e:
        logger.error(f"Broadcast task update error: {e}")

def broadcast_task_progress(socketio: SocketIO, task_id: str, progress_data: Dict[str, Any]):
    """广播任务进度"""
    try:
        socketio.emit('task_progress', {
            'type': 'task_progress',
            'data': {
                'taskId': task_id,
                **progress_data
            },
            'timestamp': datetime.utcnow().isoformat(),
        })
        logger.debug(f"Broadcasted progress for task {task_id}: {progress_data.get('progress', 0)}%")
    except Exception as e:
        logger.error(f"Broadcast task progress error: {e}")

def broadcast_system_status(socketio: SocketIO, status_data: Dict[str, Any]):
    """广播系统状态"""
    try:
        socketio.emit('system_status', {
            'type': 'system_status',
            'data': status_data,
            'timestamp': datetime.utcnow().isoformat(),
        })
        logger.debug("Broadcasted system status update")
    except Exception as e:
        logger.error(f"Broadcast system status error: {e}")

def send_notification(socketio: SocketIO, user_id: str, notification: Dict[str, Any]):
    """发送通知给特定用户"""
    try:
        socketio.emit('notification', {
            'type': 'notification',
            'data': notification,
            'timestamp': datetime.utcnow().isoformat(),
        }, room=f"user_{user_id}")
        logger.debug(f"Sent notification to user {user_id}")
    except Exception as e:
        logger.error(f"Send notification error: {e}")

def get_connected_clients() -> List[Dict[str, Any]]:
    """获取连接的客户端列表"""
    return ws_manager.get_connected_users()
