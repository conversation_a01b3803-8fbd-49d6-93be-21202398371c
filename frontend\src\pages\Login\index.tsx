import React from 'react';
import { Form, Input, Button, message, Space, Card, FloatButton } from 'antd';
import { UserOutlined, LockOutlined, ApiOutlined, BulbOutlined, BulbFilled } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { apiClient } from '@/services/api';
import { getSystemTheme } from '@/utils/theme';

interface LoginForm {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = React.useState(false);
  const [testLoading, setTestLoading] = React.useState(false);
  const [apiStatus, setApiStatus] = React.useState<'unknown' | 'connected' | 'error'>('unknown');

  // 主题状态管理
  const [themeMode, setThemeMode] = React.useState<'light' | 'dark' | 'auto'>(() => {
    const saved = localStorage.getItem('theme-mode');
    return (saved as 'light' | 'dark' | 'auto') || 'auto';
  });

  const getCurrentActualMode = () => {
    if (themeMode === 'auto') {
      return getSystemTheme();
    }
    return themeMode;
  };

  const isDarkMode = getCurrentActualMode() === 'dark';

  const toggleTheme = () => {
    const currentActual = getCurrentActualMode();
    const newMode = currentActual === 'dark' ? 'light' : 'dark';
    setThemeMode(newMode);
    localStorage.setItem('theme-mode', newMode);
    localStorage.setItem('theme', newMode); // 兼容旧版本

    // 触发存储事件以通知其他组件
    window.dispatchEvent(new StorageEvent('storage', {
      key: 'theme-mode',
      newValue: newMode,
      oldValue: currentActual
    }));

    message.success(`已切换到${newMode === 'dark' ? '暗色' : '亮色'}模式`);
  };

  // 监听系统主题变化和存储变化
  React.useEffect(() => {
    // 监听系统主题变化
    if (themeMode === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => {
        setThemeMode('auto'); // 触发重新渲染
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [themeMode]);

  // 监听localStorage变化
  React.useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'theme-mode') {
        setThemeMode((e.newValue as 'light' | 'dark' | 'auto') || 'auto');
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const testApiConnection = async () => {
    setTestLoading(true);
    try {
      const response = await apiClient.get('/health');
      console.log('API Health Check:', response);
      setApiStatus('connected');
      message.success('API连接成功！');
    } catch (error) {
      console.error('API连接失败:', error);
      setApiStatus('error');
      message.error('API连接失败，请检查后端服务是否运行');
    } finally {
      setTestLoading(false);
    }
  };

  const onFinish = async (values: LoginForm) => {
    setLoading(true);
    try {
      console.log('Login values:', values);

      // 调用真实的登录API
      const response = await apiClient.post('/api/auth/login', {
        username: values.username,
        password: values.password
      });

      console.log('Login response:', response);

      // 保存认证token
      if (response.success && response.data && response.data.token) {
        localStorage.setItem('auth_token', response.data.token);
        localStorage.setItem('user_info', JSON.stringify(response.data.user));
        message.success('登录成功！');
        navigate('/dashboard');
      } else {
        message.error('登录失败：' + (response.error || '未知错误'));
      }
    } catch (error: any) {
      console.error('Login error:', error);
      const errorMessage = error.response?.data?.error || error.message || '登录失败，请检查用户名和密码';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* API连接测试卡片 */}
      <Card
        title="API连接测试"
        style={{ marginBottom: 24 }}
        size="small"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            状态: {apiStatus === 'unknown' && '未测试'}
                  {apiStatus === 'connected' && <span style={{ color: 'green' }}>✓ 已连接</span>}
                  {apiStatus === 'error' && <span style={{ color: 'red' }}>✗ 连接失败</span>}
          </div>
          <Button
            icon={<ApiOutlined />}
            onClick={testApiConnection}
            loading={testLoading}
            type="dashed"
            block
          >
            测试API连接
          </Button>
        </Space>
      </Card>

      <Form
        name="login"
        onFinish={onFinish}
        autoComplete="off"
        size="large"
      >
        <Form.Item
        name="username"
        rules={[{ required: true, message: '请输入用户名!' }]}
      >
          <Input
            prefix={<UserOutlined />}
            placeholder="用户名"
          />
        </Form.Item>

        <Form.Item
        name="password"
        rules={[{ required: true, message: '请输入密码!' }]}
      >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="密码"
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            style={{ width: '100%' }}
          >
            登录
          </Button>
        </Form.Item>
      </Form>

      {/* 主题切换浮动按钮 */}
      <FloatButton
        icon={isDarkMode ? <BulbFilled /> : <BulbOutlined />}
        tooltip={`切换到${isDarkMode ? '亮色' : '暗色'}模式`}
        onClick={toggleTheme}
        style={{
          position: 'fixed',
          bottom: 24,
          right: 24,
        }}
      />
    </div>
  );
};

export default Login;
