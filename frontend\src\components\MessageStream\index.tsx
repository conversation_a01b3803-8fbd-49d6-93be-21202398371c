import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  List,
  Avatar,
  Typography,
  Tag,
  Space,
  Button,
  Tooltip,
  Badge,
  Empty,
} from 'antd';
import {
  UserOutlined,
  ClockCircleOutlined,
  ShareAltOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface Message {
  id: string;
  content: string;
  sender: string;
  timestamp: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'document';
  forwarded: boolean;
  groupName: string;
}

interface MessageStreamProps {
  isListening: boolean;
  onToggleListening: () => void;
  messages: Message[];
  maxMessages?: number;
}

const MessageStream: React.FC<MessageStreamProps> = ({
  isListening,
  onToggleListening,
  messages,
  maxMessages = 100,
}) => {
  const [displayMessages, setDisplayMessages] = useState<Message[]>([]);
  const listRef = useRef<HTMLDivElement>(null);
  const [autoScroll, setAutoScroll] = useState(true);

  useEffect(() => {
    // 限制显示的消息数量
    const limitedMessages = messages.slice(-maxMessages);
    setDisplayMessages(limitedMessages);

    // 自动滚动到底部
    if (autoScroll && listRef.current) {
      setTimeout(() => {
        listRef.current?.scrollTo({
          top: listRef.current.scrollHeight,
          behavior: 'smooth',
        });
      }, 100);
    }
  }, [messages, maxMessages, autoScroll]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    const isAtBottom = scrollHeight - scrollTop <= clientHeight + 50;
    setAutoScroll(isAtBottom);
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'image':
        return '🖼️';
      case 'video':
        return '🎥';
      case 'audio':
        return '🎵';
      case 'document':
        return '📄';
      default:
        return '💬';
    }
  };

  const getMessageTypeColor = (type: string) => {
    switch (type) {
      case 'image':
        return 'blue';
      case 'video':
        return 'purple';
      case 'audio':
        return 'green';
      case 'document':
        return 'orange';
      default:
        return 'default';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) {
      return '刚刚';
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <Card
      title={
        <Space>
          <Badge status={isListening ? 'processing' : 'default'} />
          实时消息流
          <Text type="secondary">({displayMessages.length}/{maxMessages})</Text>
        </Space>
      }
      extra={
        <Space>
          <Tooltip title={autoScroll ? '已开启自动滚动' : '已关闭自动滚动'}>
            <Button
              type="text"
              size="small"
              onClick={() => setAutoScroll(!autoScroll)}
            >
              {autoScroll ? '🔒' : '🔓'}
            </Button>
          </Tooltip>
          <Button
            type={isListening ? 'default' : 'primary'}
            size="small"
            icon={isListening ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={onToggleListening}
          >
            {isListening ? '停止监听' : '开始监听'}
          </Button>
        </Space>
      }
      size="small"
    >
      <div
        ref={listRef}
        style={{
          height: 400,
          overflowY: 'auto',
          border: '1px solid #f0f0f0',
          borderRadius: 6,
          padding: 8,
        }}
        onScroll={handleScroll}
      >
        {displayMessages.length === 0 ? (
          <Empty
            description={isListening ? '等待新消息...' : '点击开始监听以接收消息'}
            style={{ marginTop: 100 }}
          />
        ) : (
          <List
            dataSource={displayMessages}
            renderItem={(message) => (
              <List.Item
                style={{
                  padding: '8px 0',
                  borderBottom: '1px solid #f5f5f5',
                  backgroundColor: message.forwarded ? '#f6ffed' : 'transparent',
                }}
              >
                <List.Item.Meta
                  avatar={<Avatar size="small" icon={<UserOutlined />} />}
                  title={
                    <Space size="small">
                      <Text strong style={{ fontSize: '12px' }}>
                        {message.sender}
                      </Text>
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        来自 {message.groupName}
                      </Text>
                      <Tag
                        color={getMessageTypeColor(message.type)}
                        size="small"
                        style={{ fontSize: '10px' }}
                      >
                        {getMessageTypeIcon(message.type)} {message.type}
                      </Tag>
                      {message.forwarded && (
                        <Tag color="green" size="small" icon={<ShareAltOutlined />}>
                          已转发
                        </Tag>
                      )}
                    </Space>
                  }
                  description={
                    <div>
                      <Paragraph
                        style={{
                          margin: 0,
                          fontSize: '13px',
                          lineHeight: 1.4,
                        }}
                        ellipsis={{ rows: 2, expandable: true, symbol: '展开' }}
                      >
                        {message.content}
                      </Paragraph>
                      <Space style={{ marginTop: 4 }}>
                        <ClockCircleOutlined style={{ fontSize: '10px' }} />
                        <Text type="secondary" style={{ fontSize: '10px' }}>
                          {formatTimestamp(message.timestamp)}
                        </Text>
                      </Space>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </div>
    </Card>
  );
};

export default MessageStream;
