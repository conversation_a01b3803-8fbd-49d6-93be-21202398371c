import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Space, Typography, Drawer, message } from 'antd';
import type { MenuProps } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  SoundOutlined,
  SettingOutlined,

} from '@ant-design/icons';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useResponsive, getResponsiveLayout } from '@/utils/responsive';
import FeedbackWidget from '@/components/FeedbackWidget';
import UserMenu from '@/components/UserMenu';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

interface MainLayoutProps {
  onThemeToggle: () => void;
  isDarkMode: boolean;
  onOpenThemeSettings?: () => void;
}

const MainLayout: React.FC<MainLayoutProps> = ({ onThemeToggle, isDarkMode, onOpenThemeSettings }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { isMobile, isTablet, breakpoint } = useResponsive();
  const layoutConfig = getResponsiveLayout();

  // 处理退出登录
  const handleLogout = () => {
    console.log('退出登录按钮被点击');

    // 清除认证信息
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_info');
    localStorage.removeItem('refresh_token');

    // 显示退出成功消息
    message.success('已成功退出登录');

    // 导航到登录页面
    navigate('/login');
  };

  const menuItems: MenuProps['items'] = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/download',
      icon: <DownloadOutlined />,
      label: '下载管理',
    },
    {
      key: '/forward',
      icon: <ShareAltOutlined />,
      label: '转发管理',
    },
    {
      key: '/listen',
      icon: <SoundOutlined />,
      label: '监听转发',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];



  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
    if (isMobile) {
      setMobileMenuVisible(false);
    }
  };

  // 响应式处理
  useEffect(() => {
    if (isMobile) {
      setCollapsed(true);
    } else {
      setMobileMenuVisible(false);
    }
  }, [isMobile]);

  const toggleMenu = () => {
    if (isMobile) {
      setMobileMenuVisible(!mobileMenuVisible);
    } else {
      setCollapsed(!collapsed);
    }
  };

  const renderSiderContent = () => (
    <>
      <div
        style={{
          height: 32,
          margin: 16,
          background: isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(24, 144, 255, 0.1)',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: isDarkMode ? 'white' : '#1890ff',
          fontWeight: 'bold',
          border: isDarkMode ? 'none' : '1px solid rgba(24, 144, 255, 0.3)',
        }}
      >
        {(collapsed && !isMobile) ? 'TDL' : 'Telegram DL'}
      </div>
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
      />
    </>
  );

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          width={layoutConfig.sider.width}
          collapsedWidth={layoutConfig.sider.collapsedWidth}
          breakpoint={layoutConfig.sider.breakpoint}
          style={{
            overflow: 'auto',
            height: '100vh',
            position: 'fixed',
            left: 0,
            top: 0,
            bottom: 0,
            zIndex: 100,
          }}
        >
          {renderSiderContent()}
        </Sider>
      )}

      {/* 移动端抽屉菜单 */}
      {isMobile && (
        <Drawer
          title="导航菜单"
          placement="left"
          onClose={() => setMobileMenuVisible(false)}
          open={mobileMenuVisible}
          bodyStyle={{ padding: 0 }}
          headerStyle={{ display: 'none' }}
          width={layoutConfig.drawer.width}
        >
          <div style={{ background: '#001529', minHeight: '100%' }}>
            {renderSiderContent()}
          </div>
        </Drawer>
      )}
      <Layout
        style={{
          marginLeft: isMobile ? 0 : (collapsed ? layoutConfig.sider.collapsedWidth : layoutConfig.sider.width),
          transition: 'margin-left 0.2s',
        }}
      >
        <Header
          style={{
            padding: isMobile ? '0 8px' : '0 16px',
            background: 'var(--ant-color-bg-container)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: '1px solid var(--ant-color-border)',
            position: isMobile ? 'sticky' : 'static',
            top: 0,
            zIndex: 99,
          }}
        >
          <Space>
            <Button
              type="text"
              icon={isMobile ? <MenuUnfoldOutlined /> : (collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />)}
              onClick={toggleMenu}
              style={{
                fontSize: '16px',
                width: isMobile ? 48 : 64,
                height: isMobile ? 48 : 64,
              }}
            />
            {!isMobile && (
              <Text>欢迎使用 Telegram Media Downloader</Text>
            )}
          </Space>

          <UserMenu
            isMobile={isMobile}
            isDarkMode={isDarkMode}
            onThemeToggle={onThemeToggle}
            onOpenThemeSettings={onOpenThemeSettings}
            onLogout={handleLogout}
          />
        </Header>

        <Content
          style={{
            margin: layoutConfig.content.margin,
            padding: layoutConfig.content.padding,
            background: 'var(--ant-color-bg-container)',
            borderRadius: 6,
            overflow: 'auto',
            minHeight: isMobile ? 'calc(100vh - 64px)' : 'calc(100vh - 112px)',
          }}
        >
          <Outlet />
        </Content>
      </Layout>

      {/* 反馈组件 */}
      <FeedbackWidget />
    </Layout>
  );
};

export default MainLayout;
