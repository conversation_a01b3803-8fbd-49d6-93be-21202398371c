"""
工具函数模块
"""

import os
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from flask import jsonify

def setup_logging(level: str = 'INFO'):
    """设置日志配置"""
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    # 创建日志目录
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 配置根日志记录器
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'app.log')),
            logging.StreamHandler()
        ]
    )
    
    # 设置第三方库的日志级别
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

def generate_response(success: bool = True, data: Any = None, message: str = '', 
                     error: str = '', code: str = '') -> Dict[str, Any]:
    """生成统一的API响应格式"""
    response = {
        'success': success,
        'timestamp': datetime.utcnow().isoformat() + 'Z',
    }
    
    if data is not None:
        response['data'] = data
    
    if message:
        response['message'] = message
    
    if error:
        response['error'] = error
    
    if code:
        response['code'] = code
    
    return jsonify(response)

def validate_request_data(data: Dict[str, Any], required_fields: List[str]) -> bool:
    """验证请求数据是否包含必需字段"""
    if not data:
        return False
    
    for field in required_fields:
        if field not in data or data[field] is None:
            return False
    
    return True

def generate_id() -> str:
    """生成唯一ID"""
    return str(uuid.uuid4())

def format_bytes(bytes_value: int) -> str:
    """格式化字节数为人类可读格式"""
    if bytes_value == 0:
        return "0 B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    unit_index = 0
    size = float(bytes_value)
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    return f"{size:.1f} {units[unit_index]}"

def format_duration(seconds: int) -> str:
    """格式化持续时间"""
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{minutes}分{remaining_seconds}秒"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        return f"{hours}小时{remaining_minutes}分钟"

def sanitize_filename(filename: str) -> str:
    """清理文件名，移除不安全字符"""
    import re
    
    # 移除或替换不安全字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # 移除控制字符
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    
    # 限制长度
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename.strip()

def ensure_directory(path: str) -> bool:
    """确保目录存在，如果不存在则创建"""
    try:
        if not os.path.exists(path):
            os.makedirs(path, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"Failed to create directory {path}: {e}")
        return False

def get_file_size(file_path: str) -> int:
    """获取文件大小"""
    try:
        return os.path.getsize(file_path)
    except (OSError, IOError):
        return 0

def is_valid_file_type(filename: str, allowed_types: List[str]) -> bool:
    """检查文件类型是否允许"""
    if not allowed_types:
        return True
    
    file_ext = os.path.splitext(filename)[1].lower()
    
    # 定义文件类型映射
    type_mappings = {
        'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'],
        'video': ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v'],
        'audio': ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'],
        'document': ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt', '.xls', '.xlsx', '.ppt', '.pptx'],
        'archive': ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'],
    }
    
    for file_type in allowed_types:
        if file_type in type_mappings:
            if file_ext in type_mappings[file_type]:
                return True
        elif file_ext == f'.{file_type}':
            return True
    
    return False

def calculate_download_speed(bytes_downloaded: int, elapsed_time: float) -> str:
    """计算下载速度"""
    if elapsed_time <= 0:
        return "0 B/s"
    
    speed_bps = bytes_downloaded / elapsed_time
    return f"{format_bytes(int(speed_bps))}/s"

def parse_message_range(start_id: Optional[int], end_id: Optional[int]) -> tuple:
    """解析消息范围"""
    if start_id is None:
        start_id = 1
    
    if end_id is None or end_id <= 0:
        end_id = None  # 表示到最新消息
    
    if start_id < 1:
        start_id = 1
    
    if end_id is not None and start_id > end_id:
        start_id, end_id = end_id, start_id
    
    return start_id, end_id

def validate_phone_number(phone: str) -> bool:
    """验证手机号格式"""
    import re
    
    # 简单的手机号验证（支持国际格式）
    pattern = r'^\+?[1-9]\d{1,14}$'
    return bool(re.match(pattern, phone.replace(' ', '').replace('-', '')))

def mask_phone_number(phone: str) -> str:
    """掩码手机号"""
    if len(phone) <= 4:
        return phone
    
    return phone[:3] + '*' * (len(phone) - 6) + phone[-3:]

def get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    import psutil
    import platform
    
    try:
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_total = memory.total
        memory_used = memory.used
        memory_percent = memory.percent
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        disk_total = disk.total
        disk_used = disk.used
        disk_percent = (disk_used / disk_total) * 100
        
        # 网络信息
        network = psutil.net_io_counters()
        
        return {
            'platform': platform.system(),
            'platform_version': platform.version(),
            'python_version': platform.python_version(),
            'cpu': {
                'count': cpu_count,
                'percent': cpu_percent,
            },
            'memory': {
                'total': memory_total,
                'used': memory_used,
                'percent': memory_percent,
                'total_formatted': format_bytes(memory_total),
                'used_formatted': format_bytes(memory_used),
            },
            'disk': {
                'total': disk_total,
                'used': disk_used,
                'percent': disk_percent,
                'total_formatted': format_bytes(disk_total),
                'used_formatted': format_bytes(disk_used),
            },
            'network': {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'bytes_sent_formatted': format_bytes(network.bytes_sent),
                'bytes_recv_formatted': format_bytes(network.bytes_recv),
            },
        }
    except Exception as e:
        logging.error(f"Failed to get system info: {e}")
        return {}

def rate_limit_key(user_id: str, endpoint: str) -> str:
    """生成速率限制键"""
    return f"rate_limit:{user_id}:{endpoint}"

def is_safe_path(path: str, base_path: str) -> bool:
    """检查路径是否安全（防止路径遍历攻击）"""
    try:
        # 规范化路径
        abs_path = os.path.abspath(path)
        abs_base = os.path.abspath(base_path)
        
        # 检查是否在基础路径内
        return abs_path.startswith(abs_base)
    except Exception:
        return False

class TaskStatus:
    """任务状态常量"""
    PENDING = 'pending'
    RUNNING = 'running'
    PAUSED = 'paused'
    COMPLETED = 'completed'
    FAILED = 'failed'
    CANCELLED = 'cancelled'

class TaskType:
    """任务类型常量"""
    DOWNLOAD = 'download'
    FORWARD = 'forward'
    LISTEN_FORWARD = 'listen_forward'

class FileType:
    """文件类型常量"""
    IMAGE = 'image'
    VIDEO = 'video'
    AUDIO = 'audio'
    DOCUMENT = 'document'
    ARCHIVE = 'archive'
    ALL = 'all'
