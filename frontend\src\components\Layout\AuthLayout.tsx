import React from 'react';
import { Layout, Typography, Space, theme } from 'antd';
import { Outlet } from 'react-router-dom';

const { Content } = Layout;
const { Title, Text } = Typography;

const AuthLayout: React.FC = () => {
  const { token } = theme.useToken();

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Content
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        <div
          style={{
            width: '100%',
            maxWidth: 400,
            padding: '40px',
            background: token.colorBgContainer,
            borderRadius: 12,
            boxShadow: token.boxShadowSecondary,
            border: `1px solid ${token.colorBorder}`,
          }}
        >
          <Space direction="vertical" size="large" style={{ width: '100%', textAlign: 'center' }}>
            <div>
              <Title level={2} style={{ margin: 0, color: token.colorPrimary }}>
                Telegram Media Downloader
              </Title>
              <Text type="secondary">现代化的 Telegram 媒体下载工具</Text>
            </div>
            <Outlet />
          </Space>
        </div>
      </Content>
    </Layout>
  );
};

export default AuthLayout;
