import { useEffect, useState } from 'react';

// 断点定义
export const breakpoints = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600,
} as const;

export type Breakpoint = keyof typeof breakpoints;

// 获取当前屏幕尺寸对应的断点
export const getCurrentBreakpoint = (width: number): Breakpoint => {
  if (width >= breakpoints.xxl) return 'xxl';
  if (width >= breakpoints.xl) return 'xl';
  if (width >= breakpoints.lg) return 'lg';
  if (width >= breakpoints.md) return 'md';
  if (width >= breakpoints.sm) return 'sm';
  return 'xs';
};

// 检查是否为移动设备
export const isMobile = (width: number): boolean => {
  return width < breakpoints.md;
};

// 检查是否为平板设备
export const isTablet = (width: number): boolean => {
  return width >= breakpoints.md && width < breakpoints.lg;
};

// 检查是否为桌面设备
export const isDesktop = (width: number): boolean => {
  return width >= breakpoints.lg;
};

// 响应式Hook
export const useResponsive = () => {
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
  });

  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const currentBreakpoint = getCurrentBreakpoint(screenSize.width);

  return {
    ...screenSize,
    breakpoint: currentBreakpoint,
    isMobile: isMobile(screenSize.width),
    isTablet: isTablet(screenSize.width),
    isDesktop: isDesktop(screenSize.width),
    isSmallScreen: screenSize.width < breakpoints.lg,
    isLargeScreen: screenSize.width >= breakpoints.xl,
  };
};

// 响应式栅格配置
export const getResponsiveGrid = (mobile: number, tablet: number, desktop: number) => ({
  xs: mobile,
  sm: mobile,
  md: tablet,
  lg: desktop,
  xl: desktop,
  xxl: desktop,
});

// 响应式间距配置
export const getResponsiveSpacing = () => {
  const { isMobile, isTablet } = useResponsive();
  
  if (isMobile) {
    return {
      padding: 8,
      margin: 8,
      gutter: [8, 8] as [number, number],
    };
  }
  
  if (isTablet) {
    return {
      padding: 12,
      margin: 12,
      gutter: [12, 12] as [number, number],
    };
  }
  
  return {
    padding: 16,
    margin: 16,
    gutter: [16, 16] as [number, number],
  };
};

// 响应式字体大小
export const getResponsiveFontSize = (base: number) => {
  const { isMobile, isTablet } = useResponsive();
  
  if (isMobile) {
    return base * 0.875; // 14px if base is 16px
  }
  
  if (isTablet) {
    return base * 0.9375; // 15px if base is 16px
  }
  
  return base;
};

// 响应式组件尺寸
export const getResponsiveSize = () => {
  const { isMobile, isTablet } = useResponsive();
  
  if (isMobile) {
    return {
      cardSize: 'small' as const,
      buttonSize: 'small' as const,
      inputSize: 'middle' as const,
      tableSize: 'small' as const,
    };
  }
  
  if (isTablet) {
    return {
      cardSize: 'small' as const,
      buttonSize: 'middle' as const,
      inputSize: 'middle' as const,
      tableSize: 'middle' as const,
    };
  }
  
  return {
    cardSize: 'default' as const,
    buttonSize: 'middle' as const,
    inputSize: 'middle' as const,
    tableSize: 'middle' as const,
  };
};

// 响应式布局配置
export const getResponsiveLayout = () => {
  const { isMobile, isTablet, width } = useResponsive();
  
  return {
    // 侧边栏配置
    sider: {
      collapsedWidth: isMobile ? 0 : 80,
      width: isMobile ? 0 : isTablet ? 180 : 200,
      breakpoint: 'lg' as const,
      collapsible: true,
      trigger: null,
    },
    
    // 内容区域配置
    content: {
      padding: isMobile ? 8 : isTablet ? 12 : 16,
      margin: isMobile ? 8 : isTablet ? 12 : 16,
    },
    
    // 表格配置
    table: {
      scroll: { x: isMobile ? 800 : isTablet ? 1000 : undefined },
      pagination: {
        pageSize: isMobile ? 5 : isTablet ? 8 : 10,
        showSizeChanger: !isMobile,
        showQuickJumper: !isMobile,
        showTotal: !isMobile,
        simple: isMobile,
      },
    },
    
    // 模态框配置
    modal: {
      width: isMobile ? '95%' : isTablet ? '80%' : 600,
      centered: isMobile,
    },
    
    // 抽屉配置
    drawer: {
      width: isMobile ? '85%' : isTablet ? '60%' : 400,
      placement: 'right' as const,
    },
  };
};

// 媒体查询CSS-in-JS
export const mediaQueries = {
  mobile: `@media (max-width: ${breakpoints.md - 1}px)`,
  tablet: `@media (min-width: ${breakpoints.md}px) and (max-width: ${breakpoints.lg - 1}px)`,
  desktop: `@media (min-width: ${breakpoints.lg}px)`,
  largeDesktop: `@media (min-width: ${breakpoints.xl}px)`,
};

// 响应式样式生成器
export const createResponsiveStyles = (styles: {
  mobile?: React.CSSProperties;
  tablet?: React.CSSProperties;
  desktop?: React.CSSProperties;
}) => {
  const { isMobile, isTablet, isDesktop } = useResponsive();
  
  if (isMobile && styles.mobile) return styles.mobile;
  if (isTablet && styles.tablet) return styles.tablet;
  if (isDesktop && styles.desktop) return styles.desktop;
  
  return {};
};

// 设备检测
export const getDeviceType = () => {
  const { isMobile, isTablet, isDesktop } = useResponsive();
  
  if (isMobile) return 'mobile';
  if (isTablet) return 'tablet';
  if (isDesktop) return 'desktop';
  
  return 'unknown';
};

// 触摸设备检测
export const isTouchDevice = (): boolean => {
  return typeof window !== 'undefined' && 'ontouchstart' in window;
};
