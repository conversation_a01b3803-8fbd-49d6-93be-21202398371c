"""Bot for media downloader"""

import asyncio
import os
import re
from datetime import datetime
from typing import Callable, List, Union

import pyrogram
from loguru import logger
from pyrogram import types
from pyrogram.handlers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessageHand<PERSON>
from pyrogram.types import InlineKeyboardButton, InlineKeyboardMarkup
from ruamel import yaml
import emoji
import base64
import hashlib

import utils
from module.app import (
    Application,
    ChatDownloadConfig,
    ForwardStatus,
    QueryHandler,
    QueryHandlerStr,
    TaskNode,
    TaskType,
    UploadStatus,
)
from module.filter import Filter
from module.get_chat_history_v2 import get_chat_history_v2
from module.language import Language, _t
from module.pyrogram_extension import (
    check_user_permission,
    get_utf16_length,
    parse_link,
    proc_cache_forward,
    report_bot_forward_status,
    report_bot_status,
    retry,
    set_meta_data,
    upload_telegram_chat_message,
    get_media_group_with_retry,
)
from utils.format import replace_date_time, validate_title
from utils.meta_data import MetaData

# pylint: disable = C0301, R0902


class DownloadBot:
    """Download bot"""

    def __init__(self):
        self.bot = None
        self.client = None
        self.add_download_task: Callable = None
        self.download_chat_task: Callable = None
        self.app = None
        self.listen_forward_chat: dict = {}
        self.config: dict = {}
        self._yaml = yaml.YAML()
        self.config_path = os.path.join(os.path.abspath("."), "bot.yaml")
        self.download_command: dict = {}
        self.filter = Filter()
        self.bot_info = None
        self.task_node: dict = {}
        self.is_running = True
        self.allowed_user_ids: List[Union[int, str]] = []
        self.monitor_task = None
        
        # For persistent listen_forward tasks
        self.listen_forward_configs: dict = {}
        self.last_message_ids: dict = {}
        
        # For temporary regex pattern storage
        self.temp_regex_patterns: dict = {}

        meta = MetaData(datetime(2022, 8, 5, 14, 35, 12), 0, "", 0, 0, 0, "", 0)
        self.filter.set_meta_data(meta)

        self.download_filter: List[str] = []
        self.task_id: int = 0
        self.reply_task = None

    def gen_task_id(self) -> int:
        """Gen task id"""
        self.task_id += 1
        return self.task_id

    def add_task_node(self, node: TaskNode):
        """Add task node"""
        self.task_node[node.task_id] = node

    def remove_task_node(self, task_id: int):
        """Remove task node"""
        self.task_node.pop(task_id)

    def stop_task(self, task_id: str):
        """Stop task"""
        if task_id == "all":
            for value in self.task_node.values():
                value.stop_transmission()
        else:
            try:
                task = self.task_node.get(int(task_id))
                if task:
                    task.stop_transmission()
            except Exception:
                return

    async def update_reply_message(self):
        """Update reply message"""
        while self.is_running:
            for key, value in self.task_node.copy().items():
                if value.is_running:
                    await report_bot_status(self.bot, value)

            for key, value in self.task_node.copy().items():
                if value.is_running and value.is_finish():
                    self.remove_task_node(key)
            await asyncio.sleep(3)

    def assign_config(self, _config: dict):
        """assign config from str.

        Parameters
        ----------
        _config: dict
            application config dict

        Returns
        -------
        bool
        """

        self.download_filter = _config.get("download_filter", self.download_filter)
        self.listen_forward_configs = _config.get("listen_forward_configs", {})
        self.last_message_ids = _config.get("last_message_ids", {})

        return True

    def update_config(self):
        """Update config from str."""
        self.config["download_filter"] = self.download_filter
        self.config["listen_forward_configs"] = self.listen_forward_configs
        self.config["last_message_ids"] = self.last_message_ids

        with open(self.config_path, "w", encoding="utf-8") as yaml_file:
            self._yaml.dump(self.config, yaml_file)

    def save_listen_forward_config(self, src_chat_id: int, src_chat_title: str, dst_chat_id: int, dst_chat_title: str, download_filter: str = None, topic_id: int = None):
        """Save listen forward configuration"""
        config_key = str(src_chat_id)
        self.listen_forward_configs[config_key] = {
            "src_chat_id": src_chat_id,
            "src_chat_title": src_chat_title,
            "dst_chat_id": dst_chat_id,
            "dst_chat_title": dst_chat_title,
            "download_filter": download_filter,
            "topic_id": topic_id,
            "created_at": datetime.now().isoformat()
        }
        self.update_config()

    def remove_listen_forward_config(self, src_chat_id: int):
        """Remove listen forward configuration"""
        config_key = str(src_chat_id)
        if config_key in self.listen_forward_configs:
            del self.listen_forward_configs[config_key]
            self.update_config()
            return True
        return False

    async def restore_listen_forward_tasks(self):
        """Restore listen forward tasks from config"""
        for config_key, config in self.listen_forward_configs.items():
            try:
                # Create a simplified TaskNode for monitoring
                node = TaskNode(
                    chat_id=config["src_chat_id"],
                    from_user_id=0,  # System restored task
                    upload_telegram_chat_id=config["dst_chat_id"],
                    reply_message_id=0,
                    replay_message="Restored listen forward task",
                    download_filter=config.get("download_filter"),
                    bot=self.bot,
                    task_id=self.gen_task_id(),
                    task_type=TaskType.ListenForward,
                    topic_id=config.get("topic_id"),
                )
                
                # Get chat info
                try:
                    src_chat = await self.client.get_chat(config["src_chat_id"])
                    dst_chat = await self.client.get_chat(config["dst_chat_id"])
                    node.has_protected_content = src_chat.has_protected_content
                    
                    # Set upload user
                    me = await self.client.get_me()
                    node.upload_user = self.client
                    if not dst_chat.type is pyrogram.enums.ChatType.BOT:
                        has_permission = await check_user_permission(self.client, me.id, config["dst_chat_id"])
                        if has_permission:
                            node.upload_user = self.bot
                    
                except Exception as e:
                    logger.warning(f"Failed to get chat info for restored task {config_key}: {e}")
                    continue
                
                node.is_running = True
                self.listen_forward_chat[config["src_chat_id"]] = node
                
                logger.info(f"Restored listen forward task: {config['src_chat_title']} -> {config['dst_chat_title']}")
                
            except Exception as e:
                logger.error(f"Failed to restore listen forward task {config_key}: {e}")

        # Start monitor if we have tasks
        if self.listen_forward_chat and (not hasattr(self, "monitor_task") or self.monitor_task is None):
            self.monitor_task = self.app.loop.create_task(self.start_message_monitor())

    async def start(
        self,
        app: Application,
        client: pyrogram.Client,
        add_download_task: Callable,
        download_chat_task: Callable,
    ):
        """Start bot"""
        self.bot = pyrogram.Client(
            app.application_name + "_bot",
            api_hash=app.api_hash,
            api_id=app.api_id,
            bot_token=app.bot_token,
            workdir=app.session_file_path,
            proxy=app.proxy,
        )

        # Command list
        commands = [
            types.BotCommand("help", _t("Help")),
            types.BotCommand(
                "get_info", _t("Get group and user info from message link")
            ),
            types.BotCommand(
                "download",
                _t(
                    "To download the video, use the method to directly enter /download to view"
                ),
            ),
            types.BotCommand(
                "forward",
                _t("Forward video, use the method to directly enter /forward to view"),
            ),
            types.BotCommand(
                "listen_forward",
                _t(
                    "Listen forward, use the method to directly enter /listen_forward to view"
                ),
            ),
            types.BotCommand(
                "manage_tasks",
                _t("Manage listen forward tasks and filters")
            ),
            types.BotCommand(
                "add_filter",
                _t(
                    "Add download filter, use the method to directly enter /add_filter to view"
                ),
            ),
            types.BotCommand("check_regex", _t("Test regex filter on a message")),
            types.BotCommand("get_media", _t("Get media from message link including discussion")),
            types.BotCommand("set_language", _t("Set language")),
            types.BotCommand("stop", _t("Stop bot download or forward")),
        ]

        self.app = app
        self.client = client
        self.add_download_task = add_download_task
        self.download_chat_task = download_chat_task

        # load config
        if os.path.exists(self.config_path):
            with open(self.config_path, encoding="utf-8") as f:
                config = self._yaml.load(f.read())
                if config:
                    self.config = config
                    self.assign_config(self.config)

        await self.bot.start()

        self.bot_info = await self.bot.get_me()

        for allowed_user_id in self.app.allowed_user_ids:
            try:
                chat = await self.client.get_chat(allowed_user_id)
                self.allowed_user_ids.append(chat.id)
            except Exception as e:
                logger.warning(f"set allowed_user_ids error: {e}")

        admin = await self.client.get_me()
        self.allowed_user_ids.append(admin.id)

        await self.bot.set_bot_commands(commands)

        # Restore listen forward tasks from config
        await self.restore_listen_forward_tasks()

        self.bot.add_handler(
            MessageHandler(
                download_from_bot,
                filters=pyrogram.filters.command(["download"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )
        self.bot.add_handler(
            MessageHandler(
                forward_messages,
                filters=pyrogram.filters.command(["forward"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )
        # self.bot.add_handler(
        #     MessageHandler(
        #         download_forward_media,
        #         filters=pyrogram.filters.media
        #         & pyrogram.filters.user(self.allowed_user_ids),
        #     )
        # )
        self.bot.add_handler(
            MessageHandler(
                download_from_link,
                filters=pyrogram.filters.regex(r"^https://t.me.*")
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )
        self.bot.add_handler(
            MessageHandler(
                set_listen_forward_msg,
                filters=pyrogram.filters.command(["listen_forward"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )
        self.bot.add_handler(
            MessageHandler(
                manage_tasks_command,
                filters=pyrogram.filters.command(["manage_tasks"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )
        self.bot.add_handler(
            MessageHandler(
                help_command,
                filters=pyrogram.filters.command(["help"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )
        self.bot.add_handler(
            MessageHandler(
                get_info,
                filters=pyrogram.filters.command(["get_info"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )
        self.bot.add_handler(
            MessageHandler(
                help_command,
                filters=pyrogram.filters.command(["start"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )
        self.bot.add_handler(
            MessageHandler(
                set_language,
                filters=pyrogram.filters.command(["set_language"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )
        self.bot.add_handler(
            MessageHandler(
                add_filter,
                filters=pyrogram.filters.command(["add_filter"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )

        self.bot.add_handler(
            MessageHandler(
                stop,
                filters=pyrogram.filters.command(["stop"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )

        self.bot.add_handler(
            CallbackQueryHandler(
                on_query_handler, filters=pyrogram.filters.user(self.allowed_user_ids)
            )
        )

        try:
            await send_help_str(self.bot, admin.id)
        except Exception:
            pass

        self.reply_task = _bot.app.loop.create_task(_bot.update_reply_message())

        self.bot.add_handler(
            MessageHandler(
                forward_to_comments,
                filters=pyrogram.filters.command(["forward_to_comments"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )
        
        self.bot.add_handler(
            MessageHandler(
                add_replace_advertisement_filter,
                filters=pyrogram.filters.command(["add_replace_ad"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )
        
        self.bot.add_handler(
            MessageHandler(
                remove_replace_advertisement_filter,
                filters=pyrogram.filters.command(["remove_replace_ad"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )

        self.bot.add_handler(
            MessageHandler(
                add_filter_advertisement_filter,
                filters=pyrogram.filters.command(["add_filter_ad"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )

        self.bot.add_handler(
            MessageHandler(
                remove_filter_advertisement_filter,
                filters=pyrogram.filters.command(["remove_filter_ad"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )
        
        self.bot.add_handler(
            MessageHandler(
                set_add_advertisement,
                filters=pyrogram.filters.command(["set_add_ad"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )

        self.bot.add_handler(
            MessageHandler(
                check_regex_filter,
                filters=pyrogram.filters.command(["check_regex"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )

        self.bot.add_handler(
            MessageHandler(
                get_media_command,
                filters=pyrogram.filters.command(["get_media"])
                & pyrogram.filters.user(self.allowed_user_ids),
            )
        )

    async def check_new_messages(self, chat_id: int, node: TaskNode, last_message_id: int = 0):
        """
        Checks for new messages in the chat and forwards them.
        """
        try:
            # Only get the most recent message if last_message_id is 0
            if last_message_id == 0:
                async for message in get_chat_history_v2(
                    self.client, chat_id, limit=1
                ):
                    last_message_id = message.id
                    return last_message_id

            # Otherwise check for new messages after last_message_id
            async for message in get_chat_history_v2(
                self.client, chat_id, limit=100, offset_id=last_message_id, reverse=True
            ):
                if message.id > last_message_id:
                    # Check for advertisement filtering
                    if message.media_group_id:
                        media_group = await get_media_group_with_retry(
                            self.client, node.chat_id, message.id, 5
                        )
                        caption = ""
                        for item in media_group:
                            if item.caption:
                                caption = item.caption
                                break
                        
                        if self.app.is_match_advertisement(caption):
                            last_message_id = media_group[-1].id
                            continue
                    else:
                        text = message.caption or message.text
                        if self.app.is_match_advertisement(text):
                            last_message_id = message.id
                            continue

                    if not node.has_protected_content:
                        await forward_normal_content(self.client, node, message)
                        await report_bot_status(self.client, node, immediate_reply=True)
                    else:
                        await self.add_download_task(message, node)
                    last_message_id = message.id
        except Exception as e:
            logger.exception(f"Error checking new messages in chat {chat_id}: {e}")

        return last_message_id

    async def start_message_monitor(self):
        """
        Starts monitoring all chats that need to be forwarded.
        Runs every 60 seconds to check for new messages.
        """
        while self.is_running:
            try:
                for chat_id, node in self.listen_forward_chat.items():
                    if not node.is_running:
                        continue

                    last_id = self.last_message_ids.get(str(chat_id), 0)
                    new_last_id = await self.check_new_messages(chat_id, node, last_id)
                    if new_last_id != last_id:
                        self.last_message_ids[str(chat_id)] = new_last_id
                        self.update_config()  # Save progress

            except Exception as e:
                logger.exception(f"Error in message monitor: {e}")

            await asyncio.sleep(10)  # 每60秒检查一次


_bot = DownloadBot()


async def start_download_bot(
    app: Application,
    client: pyrogram.Client,
    add_download_task: Callable,
    download_chat_task: Callable,
):
    """Start download bot"""
    await _bot.start(app, client, add_download_task, download_chat_task)


async def stop_download_bot():
    """Stop download bot"""
    _bot.update_config()
    _bot.is_running = False
    if _bot.reply_task:
        _bot.reply_task.cancel()
    _bot.stop_task("all")
    if _bot.bot:
        await _bot.bot.stop()
    if _bot.monitor_task:
        _bot.monitor_task.cancel()
        _bot.monitor_task = None


async def send_help_str(client: pyrogram.Client, chat_id):
    """
    Sends a help string to the specified chat ID using the provided client.

    Parameters:
        client (pyrogram.Client): The Pyrogram client used to send the message.
        chat_id: The ID of the chat to which the message will be sent.

    Returns:
        str: The help string that was sent.

    Note:
        The help string includes information about the Telegram Media Downloader bot,
        its version, and the available commands.
    """

    update_keyboard = InlineKeyboardMarkup(
        [
            [
                InlineKeyboardButton(
                    _t("Github"),
                    url="https://github.com/tangyoha/telegram_media_downloader/releases",
                ),
                InlineKeyboardButton(
                    _t("Join us"), url="https://t.me/TeegramMediaDownload"
                ),
            ]
        ]
    )
    latest_release_str = ""
    # try:
    #     latest_release = get_latest_release(_bot.app.proxy)

    #     latest_release_str = (
    #         f"{_t('New Version')}: [{latest_release['name']}]({latest_release['html_url']})\an"
    #         if latest_release
    #         else ""
    #     )
    # except Exception:
    #     latest_release_str = ""

    msg = (
        f"`\n🤖 {_t('Telegram Media Downloader')}\n"
        f"🌐 {_t('Version')}: {utils.__version__}`\n"
        f"{latest_release_str}\n"
        f"{_t('Available commands:')}\n"
        f"/help - {_t('Show available commands')}\n"
        f"/get_info - {_t('Get group and user info from message link')}\n"
        f"/download - {_t('Download messages')}\n"
        f"/forward - {_t('Forward messages')}\n"
        f"/listen_forward - {_t('Listen for forwarded messages')}\n"
        f"/forward_to_comments - {_t('Forward a specific media to a comment section')}\n"
        f"/set_language - {_t('Set language')}\n"
        f"/stop - {_t('Stop bot download or forward')}\n\n"
        f"/manage_tasks - {_t('Manage listen forward tasks and filters')}\n\n"
        f"/add_replace_ad - {_t('Add regex replacement filter (removes matching text)')}\n"
        f"/remove_replace_ad - {_t('Remove regex replacement filter')}\n"
        f"/check_regex - {_t('Test regex filter on a specific message')}\n"
        f"/get_media - {_t('Get media from message link (forwards if possible, downloads if protected)')}\n"
        f"/add_filter_ad - {_t('Add advertisement filter (skips matching messages)')}\n"
        f"/remove_filter_ad - {_t('Remove advertisement filter')}\n\n"
        f"/set_add_ad - {_t('Set advertisement to add to forwarded messages')}\n\n"
        f"{_t('**Note**: 1 means the start of the entire chat')},"
        f"{_t('0 means the end of the entire chat')}\n"
        f"`[` `]` {_t('means optional, not required')}\n"
    )

    await client.send_message(chat_id, msg, reply_markup=update_keyboard)


async def manage_tasks_command(client: pyrogram.Client, message: pyrogram.types.Message):
    """
    Main management interface for tasks and filters
    """
    keyboard = InlineKeyboardMarkup([
        [
            InlineKeyboardButton(f"📋 {_t('Listen Forward Tasks')}", callback_data="manage_listen_forward"),
            InlineKeyboardButton(f"🚫 {_t('Advertisement Filters')}", callback_data="manage_ad_filters")
        ],
        [
            InlineKeyboardButton(f"🔄 {_t('Replace Ad Filters')}", callback_data="manage_replace_filters"),
            InlineKeyboardButton(f"➕ {_t('Add Advertisement Configurations')}", callback_data="manage_add_advertisement")
        ]
    ])
    
    await client.send_message(
        message.chat.id,
        f"🛠️ **{_t('Task Management')}**\n\n{_t('Choose what you want to manage:')}",
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def show_listen_forward_tasks(client: pyrogram.Client, query: pyrogram.types.CallbackQuery):
    """Show listen forward task management options"""
    keyboard = InlineKeyboardMarkup([
        [
            InlineKeyboardButton(f"🗑️ {_t('Delete')} {_t('Tasks')}", callback_data="delete_listen_forward_menu"),
            InlineKeyboardButton(f"ℹ️ {_t('View')} {_t('Tasks')}", callback_data="view_listen_forward_menu")
        ],
        [InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="back_to_manage")]
    ])
    
    await client.edit_message_text(
        query.message.chat.id,
        query.message.id,
        f"📋 **{_t('Listen Forward Tasks')}**\n\n{_t('Choose what you want to do:')}",
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def show_listen_forward_delete_menu(client: pyrogram.Client, query: pyrogram.types.CallbackQuery):
    """Show listen forward tasks for deletion"""
    if not _bot.listen_forward_configs:
        await client.edit_message_text(
            query.message.chat.id,
            query.message.id,
            f"📋 **{_t('Listen Forward Tasks')}**\n\n{_t('No active listen forward tasks.')}\n\n{_t('Use /listen_forward to add new tasks.')}",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_listen_forward")]]),
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return

    text = f"📋 **{_t('Listen Forward Tasks')} - {_t('Delete')}**\n\n"
    buttons = []
    
    for i, (config_key, config) in enumerate(_bot.listen_forward_configs.items(), 1):
        src_title = config.get("src_chat_title", _t("Unknown"))
        dst_title = config.get("dst_chat_title", _t("Unknown"))
        status = f"🟢 {_t('Active')}" if int(config_key) in _bot.listen_forward_chat else f"🔴 {_t('Stopped')}"
        
        text += f"{i}. **{src_title}** → **{dst_title}**\n"
        text += f"   {_t('Status')}: {status}\n"
        if config.get("download_filter"):
            text += f"   {_t('Filter')}: `{config['download_filter']}`\n"
        text += "\n"
        
        # Add delete button for each task
        buttons.append([
            InlineKeyboardButton(f"🗑️ {_t('Delete')} {i}", callback_data=f"delete_listen_forward_{config_key}")
        ])
    
    # Add back button
    buttons.append([InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_listen_forward")])
    
    keyboard = InlineKeyboardMarkup(buttons)
    
    await client.edit_message_text(
        query.message.chat.id,
        query.message.id,
        text,
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def show_listen_forward_view_menu(client: pyrogram.Client, query: pyrogram.types.CallbackQuery):
    """Show listen forward tasks for viewing only"""
    if not _bot.listen_forward_configs:
        await client.edit_message_text(
            query.message.chat.id,
            query.message.id,
            f"📋 **{_t('Listen Forward Tasks')}**\n\n{_t('No active listen forward tasks.')}\n\n{_t('Use /listen_forward to add new tasks.')}",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_listen_forward")]]),
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return

    text = f"📋 **{_t('Listen Forward Tasks')} - {_t('View')}**\n\n"
    
    for i, (config_key, config) in enumerate(_bot.listen_forward_configs.items(), 1):
        src_title = config.get("src_chat_title", _t("Unknown"))
        dst_title = config.get("dst_chat_title", _t("Unknown"))
        status = f"🟢 {_t('Active')}" if int(config_key) in _bot.listen_forward_chat else f"🔴 {_t('Stopped')}"
        
        text += f"{i}. **{src_title}** → **{dst_title}**\n"
        text += f"   {_t('Status')}: {status}\n"
        if config.get("download_filter"):
            text += f"   {_t('Filter')}: `{config['download_filter']}`\n"
        text += "\n"
    
    text += f"\n💡 {_t('Use /listen_forward to add new tasks.')}"
    
    keyboard = InlineKeyboardMarkup([[InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_listen_forward")]])
    
    await client.edit_message_text(
        query.message.chat.id,
        query.message.id,
        text,
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def show_advertisement_filters(client: pyrogram.Client, query: pyrogram.types.CallbackQuery):
    """Show advertisement filter management options"""
    keyboard = InlineKeyboardMarkup([
        [
            InlineKeyboardButton(f"🗑️ {_t('Delete')} {_t('Filters')}", callback_data="delete_ad_filters_menu"),
            InlineKeyboardButton(f"➕ {_t('Add New')} {_t('Filter')}", callback_data="add_ad_filter_info")
        ],
        [InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="back_to_manage")]
    ])
    
    await client.edit_message_text(
        query.message.chat.id,
        query.message.id,
        f"🚫 **{_t('Advertisement Filters')}**\n\n{_t('Choose what you want to do:')}",
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def show_advertisement_filters_delete_menu(client: pyrogram.Client, query: pyrogram.types.CallbackQuery):
    """Show advertisement filters for deletion"""
    if not _bot.app.filter_advertisement_list:
        await client.edit_message_text(
            query.message.chat.id,
            query.message.id,
            f"🚫 **{_t('Advertisement Filters')}**\n\n{_t('No advertisement filters configured.')}\n\n{_t('Use /add_filter_ad to add filters.')}",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_ad_filters")]]),
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return

    text = f"🚫 **{_t('Advertisement Filters')} - {_t('Delete')}**\n\n"
    buttons = []
    
    for i, filter_text in enumerate(_bot.app.filter_advertisement_list, 1):
        text += f"{i}. `{filter_text}`\n"
        buttons.append([
            InlineKeyboardButton(f"🗑️ {_t('Delete')} {i}", callback_data=f"delete_filter_ad_{i-1}")
        ])
    
    # Add back button
    buttons.append([InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_ad_filters")])
    
    keyboard = InlineKeyboardMarkup(buttons)
    
    await client.edit_message_text(
        query.message.chat.id,
        query.message.id,
        text,
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def show_add_ad_filter_info(client: pyrogram.Client, query: pyrogram.types.CallbackQuery):
    """Show information on how to add advertisement filters"""
    text = f"🚫 **{_t('Advertisement Filters')} - {_t('Add New')}**\n\n"
    text += f"📝 {_t('To add a new advertisement filter, use:')}\n"
    text += f"`/add_filter_ad your_filter_text`\n\n"
    text += f"📌 {_t('Example:')}\n"
    text += f"`/add_filter_ad 广告`\n"
    text += f"`/add_filter_ad 推广`\n\n"
    text += f"💡 {_t('The filter will match any message containing the specified text.')}"
    
    keyboard = InlineKeyboardMarkup([[InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_ad_filters")]])
    
    await client.edit_message_text(
        query.message.chat.id,
        query.message.id,
        text,
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def show_replace_filters(client: pyrogram.Client, query: pyrogram.types.CallbackQuery):
    """Show replace filter management options"""
    keyboard = InlineKeyboardMarkup([
        [
            InlineKeyboardButton(f"🗑️ {_t('Delete')} {_t('Filters')}", callback_data="delete_replace_filters_menu"),
            InlineKeyboardButton(f"➕ {_t('Add New')} {_t('Filter')}", callback_data="add_replace_filter_info")
        ],
        [InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="back_to_manage")]
    ])
    
    await client.edit_message_text(
        query.message.chat.id,
        query.message.id,
        f"🔄 **{_t('Replace Ad Filters')}**\n\n{_t('Choose what you want to do:')}",
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def show_replace_filters_delete_menu(client: pyrogram.Client, query: pyrogram.types.CallbackQuery):
    """Show replace filters for deletion"""
    if not _bot.app.replace_advertisement_list:
        await client.edit_message_text(
            query.message.chat.id,
            query.message.id,
            f"🔄 **{_t('Replace Ad Filters')}**\n\n{_t('No replace filters configured.')}\n\n{_t('Use /add_replace_ad to add filters.')}",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_replace_filters")]]),
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return

    text = f"🔄 **{_t('Replace Ad Filters')} - {_t('Delete')}**\n\n"
    buttons = []
    
    for i, filter_text in enumerate(_bot.app.replace_advertisement_list, 1):
        # Truncate long filters for display
        display_text = filter_text[:50] + "..." if len(filter_text) > 50 else filter_text
        text += f"{i}. `{display_text}`\n"
        buttons.append([
            InlineKeyboardButton(f"🗑️ {_t('Delete')} {i}", callback_data=f"delete_replace_ad_{i-1}")
        ])
    
    # Add back button
    buttons.append([InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_replace_filters")])
    
    keyboard = InlineKeyboardMarkup(buttons)
    
    await client.edit_message_text(
        query.message.chat.id,
        query.message.id,
        text,
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def show_add_replace_filter_info(client: pyrogram.Client, query: pyrogram.types.CallbackQuery):
    """Show information on how to add replace filters"""
    text = f"🔄 **{_t('Replace Ad Filters')} - {_t('Add New')}**\n\n"
    text += f"📝 {_t('To add a new regex replacement filter, use:')}\n"
    text += f"`/add_replace_ad regex_pattern`\n\n"
    
    text += f"📌 **{_t('Basic Examples')}:**\n"
    text += f"• `广告.*` - {_t('Matches any text starting with')} \"广告\"\n"
    text += f"• `(?i)promotion.*` - {_t('Case-insensitive match for')} \"promotion\"\n"
    text += f"• `@\\w+` - {_t('Matches any username starting with @')}\n"
    text += f"• `\\d+元` - {_t('Matches price patterns like')} \"100元\"\n"
    text += f"• `🔥.*🔥` - {_t('Matches text between fire emojis')}\n\n"
    
    text += f"🌟 **{_t('Multi-line Matching Examples')}:**\n"
    text += f"• `.*订阅.*[\\s\\S]*@\\w+` - {_t('Matches subscription ads with usernames')}\n"
    text += f"• `📣.*↓[\\s\\S]*@\\w+` - {_t('Matches announcement patterns with arrows and usernames')}\n"
    text += f"• `.*频道.*[\\s\\S]*t\\.me.*` - {_t('Matches channel promotions with Telegram links')}\n"
    text += f"• `.*投稿.*[\\s\\S]*联系.*` - {_t('Matches submission requests with contact info')}\n\n"
    
    text += f"📚 **{_t('Advanced Patterns')}:**\n"
    text += f"• `[\\s\\S]*` - {_t('Matches any character including newlines')}\n"
    text += f"• `.*?` - {_t('Non-greedy matching (shortest match)')}\n"
    text += f"• `\\s+` - {_t('Matches one or more whitespace characters')}\n"
    text += f"• `(?m)^.*订阅.*$` - {_t('Matches entire lines containing subscription')}\n\n"
    
    text += f"💡 {_t('The filter uses regex patterns to match and remove text from captions.')}\n"
    text += f"⚠️ {_t('Be careful with regex syntax - invalid patterns will be rejected.')}\n"
    text += f"🚫 {_t('Duplicate patterns will not be added.')}"
    
    keyboard = InlineKeyboardMarkup([[InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_replace_filters")]])
    
    await client.edit_message_text(
        query.message.chat.id,
        query.message.id,
        text,
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def show_add_advertisement_configs(client: pyrogram.Client, query: pyrogram.types.CallbackQuery):
    """Show add advertisement management options"""
    keyboard = InlineKeyboardMarkup([
        [
            InlineKeyboardButton(f"🗑️ {_t('Delete')} {_t('Configs')}", callback_data="delete_add_ad_configs_menu"),
            InlineKeyboardButton(f"➕ {_t('Add New')} {_t('Config')}", callback_data="add_ad_config_info")
        ],
        [InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="back_to_manage")]
    ])
    
    await client.edit_message_text(
        query.message.chat.id,
        query.message.id,
        f"➕ **{_t('Add Advertisement Configurations')}**\n\n{_t('Choose what you want to do:')}",
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def show_add_advertisement_configs_delete_menu(client: pyrogram.Client, query: pyrogram.types.CallbackQuery):
    """Show add advertisement configurations for deletion"""
    if not _bot.app.group_add_advertisement:
        await client.edit_message_text(
            query.message.chat.id,
            query.message.id,
            f"➕ **{_t('Add Advertisement Configurations')}**\n\n{_t('No add advertisement configurations.')}\n\n{_t('Use /set_add_ad to add configurations.')}",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_add_advertisement")]]),
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return

    text = f"➕ **{_t('Add Advertisement Configurations')} - {_t('Delete')}**\n\n"
    buttons = []
    
    for i, (chat_id, ad_text) in enumerate(_bot.app.group_add_advertisement.items(), 1):
        try:
            chat = await _bot.client.get_chat(chat_id)
            chat_title = chat.title or chat.first_name or str(chat_id)
        except:
            chat_title = str(chat_id)
            
        ad_display = ad_text[:30] + "..." if ad_text and len(ad_text) > 30 else (_t("None"))
        text += f"{i}. **{chat_title}**\n   {_t('Ad')}: `{ad_display}`\n\n"
        
        buttons.append([
            InlineKeyboardButton(f"🗑️ {_t('Delete')} {i}", callback_data=f"delete_add_ad_{chat_id}")
        ])
    
    # Add back button
    buttons.append([InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_add_advertisement")])
    
    keyboard = InlineKeyboardMarkup(buttons)
    
    await client.edit_message_text(
        query.message.chat.id,
        query.message.id,
        text,
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def show_add_ad_config_info(client: pyrogram.Client, query: pyrogram.types.CallbackQuery):
    """Show information on how to add advertisement configurations"""
    text = f"➕ **{_t('Add Advertisement Configurations')} - {_t('Add New')}**\n\n"
    text += f"📝 {_t('To add a new advertisement configuration, use:')}\n"
    text += f"`/set_add_ad message_link advertisement_text`\n\n"
    text += f"📌 {_t('Example:')}\n"
    text += f"`/set_add_ad https://t.me/channel/123 我的广告内容`\n\n"
    text += f"💡 {_t('This will add the specified advertisement text to messages in the target channel.')}"
    
    keyboard = InlineKeyboardMarkup([[InlineKeyboardButton(f"⬅️ {_t('Back')}", callback_data="manage_add_advertisement")]])
    
    await client.edit_message_text(
        query.message.chat.id,
        query.message.id,
        text,
        reply_markup=keyboard,
        parse_mode=pyrogram.enums.ParseMode.MARKDOWN
    )


async def help_command(client: pyrogram.Client, message: pyrogram.types.Message):
    """
    Sends a message with the available commands and their usage.

    Parameters:
        client (pyrogram.Client): The client instance.
        message (pyrogram.types.Message): The message object.

    Returns:
        None
    """

    await send_help_str(client, message.chat.id)


async def set_language(client: pyrogram.Client, message: pyrogram.types.Message):
    """
    Set the language of the bot.

    Parameters:
        client (pyrogram.Client): The pyrogram client.
        message (pyrogram.types.Message): The message containing the command.

    Returns:
        None
    """

    if len(message.text.split()) != 2:
        await client.send_message(
            message.from_user.id,
            _t("Invalid command format. Please use /set_language en/ru/zh/ua"),
        )
        return

    language = message.text.split()[1]

    try:
        language = Language[language.upper()]
        _bot.app.set_language(language)
        await client.send_message(
            message.from_user.id, f"{_t('Language set to')} {language.name}"
        )
    except KeyError:
        await client.send_message(
            message.from_user.id,
            _t("Invalid command format. Please use /set_language en/ru/zh/ua"),
        )


async def get_info(client: pyrogram.Client, message: pyrogram.types.Message):
    """
    Async function that retrieves information from a group message link.
    """

    msg = _t("Invalid command format. Please use /get_info group_message_link")

    args = message.text.split()
    if len(args) != 2:
        await client.send_message(
            message.from_user.id,
            msg,
        )
        return

    chat_id, message_id, _ = await parse_link(_bot.client, args[1])

    entity = None
    if chat_id:
        entity = await _bot.client.get_chat(chat_id)

    if entity:
        if message_id:
            _message = await retry(_bot.client.get_messages, args=(chat_id, message_id))
            if _message:
                meta_data = MetaData()
                set_meta_data(meta_data, _message)
                msg = (
                    f"`\n"
                    f"{_t('Group/Channel')}\n"
                    f"├─ {_t('id')}: {entity.id}\n"
                    f"├─ {_t('first name')}: {entity.first_name}\n"
                    f"├─ {_t('last name')}: {entity.last_name}\n"
                    f"└─ {_t('name')}: {entity.username}\n"
                    f"{_t('Message')}\n"
                )

                for key, value in meta_data.data().items():
                    if key == "send_name":
                        msg += f"└─ {key}: {value or None}\n"
                    else:
                        msg += f"├─ {key}: {value or None}\n"

                msg += "`"
    await client.send_message(
        message.from_user.id,
        msg,
    )


async def add_filter(client: pyrogram.Client, message: pyrogram.types.Message):
    """
    Set the download filter of the bot.

    Parameters:
        client (pyrogram.Client): The pyrogram client.
        message (pyrogram.types.Message): The message containing the command.

    Returns:
        None
    """

    args = message.text.split(maxsplit=1)
    if len(args) != 2:
        await client.send_message(
            message.from_user.id,
            _t("Invalid command format. Please use /add_filter your filter"),
        )
        return

    filter_str = replace_date_time(args[1])
    res, err = _bot.filter.check_filter(filter_str)
    if res:
        _bot.app.down = args[1]
        await client.send_message(
            message.from_user.id, f"{_t('Add download filter')} : {args[1]}"
        )
    else:
        await client.send_message(
            message.from_user.id, f"{err}\n{_t('Check error, please add again!')}"
        )
    return


async def add_filter_advertisement_filter(
    client: pyrogram.Client, message: pyrogram.types.Message
):
    """
    Set the download filter of the bot.

    Parameters:
        client (pyrogram.Client): The pyrogram client.
        message (pyrogram.types.Message): The message containing the command.

    Returns:
        None
    """

    args = message.text.split(maxsplit=1)
    if len(args) != 2:
        await client.send_message(
            message.from_user.id,
            _t("Invalid command format. Please use /add_ad filter"),
        )
        return

    filter_str = args[1]

    _bot.app.filter_advertisement_list.append(filter_str)
    await client.send_message(message.from_user.id, f"{_t('Add filter')} : {args[1]}")
    _bot.app.update_config(True)


async def remove_filter_advertisement_filter(
    client: pyrogram.Client, message: pyrogram.types.Message
):
    """
    Add or remove advertisement filter
    """

    args = message.text.split(maxsplit=1)
    if len(args) != 2:
        await client.send_message(
            message.from_user.id,
            _t("Invalid command format. Please use /remove_ad filter"),
        )
        return

    filter_str = args[1]
    if filter_str in _bot.app.filter_advertisement_list:
        _bot.app.filter_advertisement_list.remove(filter_str)
        await client.send_message(
            message.from_user.id, f"{_t('Remove filter')} : {args[1]}"
        )

        _bot.app.update_config(True)
    else:
        await client.send_message(
            message.from_user.id, f"{_t('Filter')} : {args[1]} {_t('not exist')}"
        )


async def set_add_advertisement(
    client: pyrogram.Client, message: pyrogram.types.Message
):
    """
    Add or remove advertisement filter
    """

    args = message.text.split(maxsplit=2)
    if len(args) < 2:
        await client.send_message(
            message.from_user.id,
            _t("Invalid command format. Please use /set_ad mesage_link advertisement"),
        )
        return

    mesage_link = args[1]
    advertisement_str = None if len(args) < 3 else args[2]

    try:
        chat_id, _, _ = await parse_link(_bot.client, mesage_link)
        _bot.app.group_add_advertisement[chat_id] = advertisement_str
        _bot.app.update_config(True)
        await client.send_message(
            message.from_user.id, f"{_t('Set advertisement')} : {advertisement_str}"
        )
    except Exception as e:
        await client.send_message(
            message.from_user.id, f"{_t('Parse link error')}: {e}"
        )
        return


class MessageProcessor:
    """Helper class for processing message captions and entities."""

    def __init__(self, raw_message, filter_str):
        """Initialize with raw message and filter string."""
        self.raw_message = raw_message
        self.filter_str = filter_str
        self.raw_caption = (raw_message.caption or raw_message.text) if raw_message else None
        self.start_offset = -1
        self.end_offset = -1
        self.filtered_entities = []
        self.raw_filter_str = None
        self.raw_caption_str = None

    def find_filter_position(self) -> bool:
        """
        Find the position of filter string in caption.
        Returns True if filter string is found, False otherwise.
        """
        if not self.raw_caption or not self.filter_str:
            return False
        
        
        try:
            # Convert to standard strings first to avoid issues with pyrogram's __getitem__
            caption_str = str(self.raw_caption)
            filter_str_std = str(self.filter_str)

            import emoji
            filter_e = emoji.demojize(filter_str_std, delimiters=("%%", "%%"), language="zh")
            raw_e = emoji.demojize(caption_str, delimiters=("%%", "%%"), language="zh")
            
            # Use the standard strings for surrogate processing as well
            self.raw_filter_str = pyrogram.parser.utils.add_surrogates(filter_str_std)
            self.raw_caption_str = pyrogram.parser.utils.add_surrogates(caption_str)
            
            self.start_offset = self.raw_caption_str.find(self.raw_filter_str)
            
            if self.start_offset == -1:
                return False
                
            # Use the standard filter string for length calculation
            self.end_offset = self.start_offset + get_utf16_length(filter_str_std)
            return True
        except Exception as e:
            logger.exception(f"find_filter_position error: {e}")
            return False


    def process_entities(self) -> bool:
        """
        Process and filter message entities.
        Returns True if processing was successful, False otherwise.
        """
        if not self.raw_message or not hasattr(self.raw_message, 'caption_entities'):
            return False
            
        if self.start_offset == -1 or self.end_offset == -1:
            return False

        try:
            entities = self.raw_message.caption_entities if self.raw_message.caption else self.raw_message.entities
            self.filtered_entities = []
            for entity in entities:
                cur_start_offset = entity.offset
                cur_end_offset = entity.offset + entity.length

                # Check if entity overlaps with filter text
                if (cur_start_offset >= self.start_offset and cur_end_offset <= self.end_offset) or \
                   (cur_start_offset < self.start_offset and cur_end_offset > self.start_offset) or \
                   (cur_start_offset < self.end_offset and cur_end_offset > self.end_offset):
                    self.filtered_entities.append(entity)

            self.filtered_entities.sort(key=lambda x: x.offset)
            return True
        except Exception as e:
            logger.exception(f"process_entities error: {e}")
            return False

    def get_total_span(self):
        """Calculate the total span for text extraction."""
        if not self.filtered_entities:
            return (self.start_offset, self.end_offset) if self.start_offset != -1 else None

        try:
            first_entity = self.filtered_entities[0]
            last_entity = self.filtered_entities[-1]
            return (
                min(self.start_offset, first_entity.offset),
                max(self.end_offset, last_entity.offset + last_entity.length)
            )
        except Exception:
            return None

    def extract_text(self, total_span) -> str:
        """
        Extract and process text with adjusted entity offsets.
        Returns empty string if extraction fails.
        """
        if not total_span or not self.raw_caption:
            return ""

        try:
            text = self.raw_caption[total_span[0]:total_span[1]]
            for entity in self.filtered_entities:
                entity.offset -= total_span[0]
            return pyrogram.parser.Parser.unparse(text, self.filtered_entities, True)
        except Exception:
            return ""


async def proc_replace_advertisement(mesage_link: str, filter_str: str) -> str:
    """
    Process and replace advertisement content in a message.

    Args:
        mesage_link (str): The link to the Telegram message
        filter_str (str): The string to filter/replace in the message caption

    Returns:
        str: The processed caption with preserved formatting and entities, or empty string if processing fails

    Note:
        This function handles various error cases and returns empty string instead of raising exceptions
    """
    try:
        chat_id, message_id, _ = await parse_link(_bot.client, mesage_link)
        if not chat_id or not message_id:
            return ""

        raw_message = await retry(_bot.client.get_messages, args=(chat_id, message_id))
        if not raw_message:
            return ""

        processor = MessageProcessor(raw_message, filter_str)
        
        # Find filter string position in caption
        if not processor.find_filter_position():
            return ""

        # Process message entities
        if not processor.process_entities():
            return ""

        # Get text span
        total_span = processor.get_total_span()
        if not total_span:
            return ""

        # Extract final text
        return processor.extract_text(total_span)

    except Exception:
        return ""


async def add_replace_advertisement_filter(
    client: pyrogram.Client, message: pyrogram.types.Message
):
    """
    Add a regex-based replacement filter for advertisements.

    Parameters:
        client (pyrogram.Client): The pyrogram client.
        message (pyrogram.types.Message): The message containing the command.

    Returns:
        None
    """

    args = message.text.split(maxsplit=1)
    if len(args) != 2:
        await client.send_message(
            message.from_user.id,
            _t("Invalid command format. Please use /add_replace_ad regex_pattern\n\n") +
            _t("Examples:\n") +
            "• `/add_replace_ad 广告.*`\n" +
            "• `/add_replace_ad (?i)promotion.*`\n" +
            "• `/add_replace_ad @\\w+`\n\n" +
            f"**{_t('Multi-line Matching Examples')}:**\n" +
            f"• `/add_replace_ad .*订阅.*[\\s\\S]*@\\w+`\n" +
            f"• `/add_replace_ad 📣.*↓[\\s\\S]*@\\w+`\n" +
            f"• `/add_replace_ad .*频道.*[\\s\\S]*t\\.me.*`\n\n" +
            f"💡 {_t('Use /check_regex to test patterns first!')}\n" +
            _t("Note: This now uses regex patterns instead of simple text matching."),
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return

    regex_pattern = args[1]

    try:
        # Validate regex pattern by trying to compile it
        re.compile(regex_pattern)
        
        # Check if pattern already exists
        if regex_pattern in _bot.app.replace_advertisement_list:
            await client.send_message(
                message.from_user.id, 
                f"ℹ️ {_t('Pattern already exists')}: `{regex_pattern}`\n\n" +
                f"💡 {_t('Use /manage_tasks to view all existing filters.')}",
                parse_mode=pyrogram.enums.ParseMode.MARKDOWN
            )
            return
        
        _bot.app.replace_advertisement_list.append(regex_pattern)
        _bot.app.update_config(True)
        await client.send_message(
            message.from_user.id, 
            f"{_t('Add regex filter')}: `{regex_pattern}`\n\n" +
            f"✅ {_t('Regex pattern validated successfully!')}",
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
    except re.error as e:
        await client.send_message(
            message.from_user.id, 
            f"❌ {_t('Invalid regex pattern')}: `{regex_pattern}`\n\n" +
            f"{_t('Error')}: {str(e)}\n\n" +
            f"{_t('Please check your regex syntax and try again.')}",
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return
    except Exception as e:
        await client.send_message(
            message.from_user.id, 
            f"❌ {_t('Error adding filter')}: `{regex_pattern}`\n\n" +
            f"{_t('Error')}: {str(e)}",
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return


async def remove_replace_advertisement_filter(
    client: pyrogram.Client, message: pyrogram.types.Message
):
    """
    Remove a regex-based replacement filter for advertisements.

    Parameters:
        client (pyrogram.Client): The pyrogram client.
        message (pyrogram.types.Message): The message containing the command.

    Returns:
        None
    """

    args = message.text.split(maxsplit=1)
    if len(args) != 2:
        await client.send_message(
            message.from_user.id,
            _t("Invalid command format. Please use /remove_replace_ad regex_pattern\n\n") +
            _t("Use /manage_tasks to view all existing filters."),
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return

    regex_pattern = args[1]

    try:
        if regex_pattern in _bot.app.replace_advertisement_list:
            _bot.app.replace_advertisement_list.remove(regex_pattern)
            await client.send_message(
                message.from_user.id, 
                f"✅ {_t('Remove filter')}: `{regex_pattern}`",
                parse_mode=pyrogram.enums.ParseMode.MARKDOWN
            )
            _bot.app.update_config(True)
        else:
            await client.send_message(
                message.from_user.id, 
                f"❌ {_t('Filter')}: `{regex_pattern}` {_t('not exist')}\n\n" +
                f"{_t('Use /manage_tasks to view all existing filters.')}",
                parse_mode=pyrogram.enums.ParseMode.MARKDOWN
            )
        
    except Exception as e:
        await client.send_message(
            message.from_user.id, 
            f"❌ {_t('Error removing filter')}: `{regex_pattern}`\n\n" +
            f"{_t('Error')}: {str(e)}",
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return


async def direct_download(
    download_bot: DownloadBot,
    chat_id: Union[str, int],
    message: pyrogram.types.Message,
    download_message: pyrogram.types.Message,
    client: pyrogram.Client = None,
):
    """Direct Download"""

    replay_message = _t("Direct download...")
    last_reply_message = await download_bot.bot.send_message(
        message.from_user.id, replay_message, reply_to_message_id=message.id
    )

    node = TaskNode(
        chat_id=chat_id,
        from_user_id=message.from_user.id,
        reply_message_id=last_reply_message.id,
        replay_message=replay_message,
        limit=1,
        bot=download_bot.bot,
        task_id=_bot.gen_task_id(),
    )

    node.client = client

    _bot.add_task_node(node)

    await _bot.add_download_task(
        download_message,
        node,
    )

    node.is_running = True


async def download_forward_media(
    client: pyrogram.Client, message: pyrogram.types.Message
):
    """
    Downloads the media from a forwarded message.

    Parameters:
        client (pyrogram.Client): The client instance.
        message (pyrogram.types.Message): The message object.

    Returns:
        None
    """

    if message.media and getattr(message, message.media.value):
        await direct_download(_bot, message.from_user.id, message, message, client)
        return

    await client.send_message(
        message.from_user.id,
        f"1. {_t('Direct download, directly forward the message to your robot')}\n\n",
        parse_mode=pyrogram.enums.ParseMode.HTML,
    )


async def download_from_link(client: pyrogram.Client, message: pyrogram.types.Message):
    """
    Downloads a single message from a Telegram link.

    Parameters:
        client (pyrogram.Client): The pyrogram client.
        message (pyrogram.types.Message): The message containing the Telegram link.

    Returns:
        None
    """

    if not message.text or not message.text.startswith("https://t.me"):
        return

    msg = (
        f"1. {_t('Directly download a single message')}\n"
        "<i>https://t.me/12000000/1</i>\n\n"
    )

    text = message.text.split()
    if len(text) != 1:
        await client.send_message(
            message.from_user.id, msg, parse_mode=pyrogram.enums.ParseMode.HTML
        )

    chat_id, message_id, _ = await parse_link(_bot.client, text[0])

    entity = None
    if chat_id:
        entity = await _bot.client.get_chat(chat_id)
    if entity:
        if message_id:
            download_message = await retry(
                _bot.client.get_messages, args=(chat_id, message_id)
            )
            if download_message:
                await direct_download(_bot, entity.id, message, download_message)
            else:
                client.send_message(
                    message.from_user.id,
                    f"{_t('From')} {entity.title} {_t('download')} {message_id} {_t('error')}!",
                    reply_to_message_id=message.id,
                )
        return

    await client.send_message(
        message.from_user.id, msg, parse_mode=pyrogram.enums.ParseMode.HTML
    )


# pylint: disable = R0912, R0915,R0914


async def download_from_bot(client: pyrogram.Client, message: pyrogram.types.Message):
    """Download from bot"""

    msg = (
        f"{_t('Parameter error, please enter according to the reference format')}:\n\n"
        f"1. {_t('Download all messages of common group')}\n"
        "<i>/download https://t.me/fkdhlg 1 0</i>\n\n"
        f"{_t('The private group (channel) link is a random group message link')}\n\n"
        f"2. {_t('The download starts from the N message to the end of the M message')}. "
        f"{_t('When M is 0, it means the last message. The filter is optional')}\n"
        f"<i>/download https://t.me/12000000 N M [filter]</i>\n\n"
    )

    args = message.text.split(maxsplit=4)
    if not message.text or len(args) < 4:
        await client.send_message(
            message.from_user.id, msg, parse_mode=pyrogram.enums.ParseMode.HTML
        )
        return

    url = args[1]
    try:
        start_offset_id = int(args[2])
        end_offset_id = int(args[3])
    except Exception:
        await client.send_message(
            message.from_user.id, msg, parse_mode=pyrogram.enums.ParseMode.HTML
        )
        return

    limit = 0
    if end_offset_id:
        if end_offset_id < start_offset_id:
            raise ValueError(
                f"end_offset_id < start_offset_id, {end_offset_id} < {start_offset_id}"
            )

        limit = end_offset_id - start_offset_id + 1

    download_filter = args[4] if len(args) > 4 else None

    if download_filter:
        download_filter = replace_date_time(download_filter)
        res, err = _bot.filter.check_filter(download_filter)
        if not res:
            await client.send_message(
                message.from_user.id, err, reply_to_message_id=message.id
            )
            return
    try:
        chat_id, _, _ = await parse_link(_bot.client, url)
        if chat_id:
            entity = await _bot.client.get_chat(chat_id)
        if entity:
            chat_title = entity.title
            reply_message = f"from {chat_title} "
            chat_download_config = ChatDownloadConfig()
            chat_download_config.last_read_message_id = start_offset_id
            chat_download_config.download_filter = download_filter
            reply_message += (
                f"download message id = {start_offset_id} - {end_offset_id} !"
            )
            last_reply_message = await client.send_message(
                message.from_user.id, reply_message, reply_to_message_id=message.id
            )
            node = TaskNode(
                chat_id=entity.id,
                from_user_id=message.from_user.id,
                reply_message_id=last_reply_message.id,
                replay_message=reply_message,
                limit=limit,
                start_offset_id=start_offset_id,
                end_offset_id=end_offset_id,
                bot=_bot.bot,
                task_id=_bot.gen_task_id(),
            )
            _bot.add_task_node(node)
            _bot.app.loop.create_task(
                _bot.download_chat_task(_bot.client, chat_download_config, node)
            )
    except Exception as e:
        await client.send_message(
            message.from_user.id,
            f"{_t('chat input error, please enter the channel or group link')}\n\n"
            f"{_t('Error type')}: {e.__class__}"
            f"{_t('Exception message')}: {e}",
        )
        return


async def get_forward_task_node(
    client: pyrogram.Client,
    message: pyrogram.types.Message,
    task_type: TaskType,
    src_chat_link: str,
    dst_chat_link: str,
    offset_id: int = 0,
    end_offset_id: int = 0,
    download_filter: str = None,
    reply_comment: bool = False,
):
    """Get task node"""
    limit: int = 0

    if end_offset_id:
        if end_offset_id < offset_id:
            await client.send_message(
                message.from_user.id,
                f" end_offset_id({end_offset_id}) < start_offset_id({offset_id}),"
                f" end_offset_id{_t('must be greater than')} offset_id",
            )
            return None

        limit = end_offset_id - offset_id + 1

    src_chat_id, _, _ = await parse_link(_bot.client, src_chat_link)
    dst_chat_id, target_msg_id, topic_id = await parse_link(_bot.client, dst_chat_link)

    if not src_chat_id or not dst_chat_id:
        logger.info(f"{src_chat_id} {dst_chat_id}")
        await client.send_message(
            message.from_user.id,
            _t("Invalid chat link") + f"{src_chat_id} {dst_chat_id}",
            reply_to_message_id=message.id,
        )
        return None

    try:
        src_chat = await _bot.client.get_chat(src_chat_id)
        dst_chat = await _bot.client.get_chat(dst_chat_id)
    except Exception as e:
        await client.send_message(
            message.from_user.id,
            f"{_t('Invalid chat link')} {e}",
            reply_to_message_id=message.id,
        )
        logger.exception(f"get chat error: {e}")
        return None

    me = await client.get_me()
    if dst_chat.id == me.id:
        # TODO: when bot receive message judge if download
        await client.send_message(
            message.from_user.id,
            _t("Cannot be forwarded to this bot, will cause an infinite loop"),
            reply_to_message_id=message.id,
        )
        return None

    if download_filter:
        download_filter = replace_date_time(download_filter)
        res, err = _bot.filter.check_filter(download_filter)
        if not res:
            await client.send_message(
                message.from_user.id, err, reply_to_message_id=message.id
            )

    last_reply_message = await client.send_message(
        message.from_user.id,
        _t("Forwarding message, please wait..."),
        reply_to_message_id=message.id,
    )

    node = TaskNode(
        chat_id=src_chat.id,
        from_user_id=message.from_user.id,
        upload_telegram_chat_id=dst_chat_id,
        reply_message_id=last_reply_message.id,
        replay_message=last_reply_message.text,
        has_protected_content=src_chat.has_protected_content,
        download_filter=download_filter,
        limit=limit,
        start_offset_id=offset_id,
        end_offset_id=end_offset_id,
        bot=_bot.bot,
        task_id=_bot.gen_task_id(),
        task_type=task_type,
        topic_id=topic_id,
    )

    if target_msg_id and reply_comment:
        node.reply_to_message = await _bot.client.get_discussion_message(
            dst_chat_id, target_msg_id
        )

    _bot.add_task_node(node)

    node.upload_user = _bot.client
    if not dst_chat.type is pyrogram.enums.ChatType.BOT:
        has_permission = await check_user_permission(_bot.client, me.id, dst_chat.id)
        if has_permission:
            node.upload_user = _bot.bot

    if node.upload_user is _bot.client:
        await client.edit_message_text(
            message.from_user.id,
            last_reply_message.id,
            _t("Note that the robot may not be in the target group, use the user account to forward"),
        )

    return node


# pylint: disable = R0914
async def forward_message_impl(
    client: pyrogram.Client, message: pyrogram.types.Message, reply_comment: bool
):
    """
    Forward message
    """

    async def report_error(client: pyrogram.Client, message: pyrogram.types.Message):
        """Report error"""

        await client.send_message(
            message.from_user.id,
            f"{_t('Invalid command format')}."
            f"{_t('Please use')} "
            "/forward https://t.me/c/src_chat https://t.me/c/dst_chat "
            f"1 400 `[`{_t('Filter')}`]`\n",
        )

    args = message.text.split(maxsplit=5)
    if len(args) < 5:
        await report_error(client, message)
        return

    src_chat_link = args[1]
    dst_chat_link = args[2]

    try:
        offset_id = int(args[3])
        end_offset_id = int(args[4])
    except Exception:
        await report_error(client, message)
        return

    download_filter = args[5] if len(args) > 5 else None

    node = await get_forward_task_node(
        client,
        message,
        TaskType.Forward,
        src_chat_link,
        dst_chat_link,
        offset_id,
        end_offset_id,
        download_filter,
        reply_comment,
    )

    if not node:
        return

    if not node.has_protected_content:
        try:
            async for item in get_chat_history_v2(  # type: ignore
                _bot.client,
                node.chat_id,
                limit=node.limit,
                max_id=node.end_offset_id,
                offset_id=offset_id,
                reverse=True,
            ):
                await forward_normal_content(client, node, item)
                if node.is_stop_transmission:
                    await client.edit_message_text(
                        message.from_user.id,
                        node.reply_message_id,
                        f"{_t('Stop Forward')}",
                    )
                    break
        except Exception as e:
            await client.edit_message_text(
                message.from_user.id,
                node.reply_message_id,
                f"{_t('Error forwarding message')} {e}",
            )
        finally:
            await report_bot_status(client, node, immediate_reply=True)
            node.stop_transmission()
    else:
        await forward_msg(node, offset_id)


async def forward_messages(client: pyrogram.Client, message: pyrogram.types.Message):
    """
    Forwards messages from one chat to another.

    Parameters:
        client (pyrogram.Client): The pyrogram client.
        message (pyrogram.types.Message): The message containing the command.

    Returns:
        None
    """
    return await forward_message_impl(client, message, False)


async def forward_normal_content(
    client: pyrogram.Client, node: TaskNode, message: pyrogram.types.Message
):
    """Forward normal content"""
    forward_ret = ForwardStatus.FailedForward
    caption = message.caption
    if caption:
        caption = validate_title(caption)
        _bot.app.set_caption_name(node.chat_id, message.media_group_id, caption)
    else:
        caption = _bot.app.get_caption_name(node.chat_id, message.media_group_id)

    if caption and _bot.app.is_match_advertisement(caption):
        forward_ret = ForwardStatus.SkipForward
        if message.media_group_id:
            # TODO
            node.upload_status[message.id] = UploadStatus.SkipUpload
        return

    if node.download_filter:
        meta_data = MetaData()
        set_meta_data(meta_data, message, caption)
        _bot.filter.set_meta_data(meta_data)
        if not _bot.filter.exec(node.download_filter):
            forward_ret = ForwardStatus.SkipForward
            if message.media_group_id:
                node.upload_status[message.id] = UploadStatus.SkipUpload
                await proc_cache_forward(_bot.client, node, message, False, _bot.app)
            await report_bot_forward_status(client, node, forward_ret)
            return

    await upload_telegram_chat_message(
        _bot.client, node.upload_user, _bot.app, node, message
    )


async def forward_msg(node: TaskNode, message_id: int):
    """Forward normal message"""

    chat_download_config = ChatDownloadConfig()
    chat_download_config.last_read_message_id = message_id
    chat_download_config.download_filter = node.download_filter  # type: ignore

    await _bot.download_chat_task(_bot.client, chat_download_config, node)


async def set_listen_forward_msg(
    client: pyrogram.Client, message: pyrogram.types.Message
):
    """
    Set the chat to listen for forwarded messages.
    """
    args = message.text.split(maxsplit=3)

    if len(args) < 3:
        await client.send_message(
            message.from_user.id,
            f"{_t('Invalid command format')}. {_t('Please use')} /listen_forward "
            f"https://t.me/c/src_chat https://t.me/c/dst_chat [{_t('Filter')}]\n",
        )
        return

    src_chat_link = args[1]
    dst_chat_link = args[2]
    download_filter = args[3] if len(args) > 3 else None

    node = await get_forward_task_node(
        client,
        message,
        TaskType.ListenForward,
        src_chat_link,
        dst_chat_link,
        download_filter=download_filter,
    )

    if not node:
        return

    if node.chat_id in _bot.listen_forward_chat:
        _bot.remove_task_node(_bot.listen_forward_chat[node.chat_id].task_id)

    node.is_running = True
    _bot.listen_forward_chat[node.chat_id] = node
    
    # Save configuration for persistence
    try:
        src_chat = await _bot.client.get_chat(node.chat_id)
        dst_chat = await _bot.client.get_chat(node.upload_telegram_chat_id)
        _bot.save_listen_forward_config(
            node.chat_id,
            src_chat.title or src_chat.first_name or str(src_chat.id),
            node.upload_telegram_chat_id,
            dst_chat.title or dst_chat.first_name or str(dst_chat.id),
            download_filter,
            node.topic_id
        )
    except Exception as e:
        logger.warning(f"Failed to save listen forward config: {e}")

    if not hasattr(_bot, "monitor_task") or _bot.monitor_task is None:
        _bot.monitor_task = _bot.app.loop.create_task(_bot.start_message_monitor())


async def stop(client: pyrogram.Client, message: pyrogram.types.Message):
    """Stops listening for forwarded messages."""

    await client.send_message(
        message.chat.id,
        _t("Please select:"),
        reply_markup=InlineKeyboardMarkup(
            [
                [
                    InlineKeyboardButton(
                        _t("Stop Download"), callback_data="stop_download"
                    ),
                    InlineKeyboardButton(
                        _t("Stop Forward"), callback_data="stop_forward"
                    ),
                ],
                [  # Second row
                    InlineKeyboardButton(
                        _t("Stop Listen Forward"), callback_data="stop_listen_forward"
                    )
                ],
            ]
        ),
    )


async def stop_task(
    client: pyrogram.Client,
    query: pyrogram.types.CallbackQuery,
    queryHandler: str,
    task_type: TaskType,
):
    """Stop task"""
    if query.data == queryHandler:
        buttons: List[InlineKeyboardButton] = []
        temp_buttons: List[InlineKeyboardButton] = []
        for key, value in _bot.task_node.copy().items():
            if not value.is_finish() and value.task_type is task_type:
                if len(temp_buttons) == 3:
                    buttons.append(temp_buttons)
                    temp_buttons = []
                temp_buttons.append(
                    InlineKeyboardButton(
                        f"{key}", callback_data=f"{queryHandler} task {key}"
                    )
                )
        if temp_buttons:
            buttons.append(temp_buttons)

        if buttons:
            buttons.insert(
                0,
                [
                    InlineKeyboardButton(
                        _t("all"), callback_data=f"{queryHandler} task all"
                    )
                ],
            )
            await client.edit_message_text(
                query.message.from_user.id,
                query.message.id,
                f"{_t('Stop')} {_t(task_type.name)}...",
                reply_markup=InlineKeyboardMarkup(buttons),
            )
        else:
            await client.edit_message_text(
                query.message.from_user.id,
                query.message.id,
                f"{_t('No Task')}",
            )
    else:
        task_id = query.data.split(" ")[2]
        await client.edit_message_text(
            query.message.from_user.id,
            query.message.id,
            f"{_t('Stop')} {_t(task_type.name)}...",
        )
        _bot.stop_task(task_id)


async def on_query_handler(
    client: pyrogram.Client, query: pyrogram.types.CallbackQuery
):
    """
    Asynchronous function that handles query callbacks.

    Parameters:
        client (pyrogram.Client): The Pyrogram client object.
        query (pyrogram.types.CallbackQuery): The callback query object.

    Returns:
        None
    """
    
    # Handle management interface callbacks
    if query.data == "manage_listen_forward":
        await show_listen_forward_tasks(client, query)
        return
    elif query.data == "manage_ad_filters":
        await show_advertisement_filters(client, query)
        return
    elif query.data == "manage_replace_filters":
        await show_replace_filters(client, query)
        return
    elif query.data == "manage_add_advertisement":
        await show_add_advertisement_configs(client, query)
        return
    elif query.data == "back_to_manage":
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton(f"📋 {_t('Listen Forward Tasks')}", callback_data="manage_listen_forward"),
                InlineKeyboardButton(f"🚫 {_t('Advertisement Filters')}", callback_data="manage_ad_filters")
            ],
            [
                InlineKeyboardButton(f"🔄 {_t('Replace Ad Filters')}", callback_data="manage_replace_filters"),
                InlineKeyboardButton(f"➕ {_t('Add Advertisement Configurations')}", callback_data="manage_add_advertisement")
            ]
        ])
        
        await client.edit_message_text(
            query.message.chat.id,
            query.message.id,
            f"🛠️ **{_t('Task Management')}**\n\n{_t('Choose what you want to manage:')}",
            reply_markup=keyboard,
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return
    
    # Handle listen forward submenu callbacks
    elif query.data == "delete_listen_forward_menu":
        await show_listen_forward_delete_menu(client, query)
        return
    elif query.data == "view_listen_forward_menu":
        await show_listen_forward_view_menu(client, query)
        return
    
    # Handle advertisement filters submenu callbacks
    elif query.data == "delete_ad_filters_menu":
        await show_advertisement_filters_delete_menu(client, query)
        return
    elif query.data == "add_ad_filter_info":
        await show_add_ad_filter_info(client, query)
        return
    
    # Handle replace filters submenu callbacks
    elif query.data == "delete_replace_filters_menu":
        await show_replace_filters_delete_menu(client, query)
        return
    elif query.data == "add_replace_filter_info":
        await show_add_replace_filter_info(client, query)
        return
    
    # Handle add advertisement submenu callbacks
    elif query.data == "delete_add_ad_configs_menu":
        await show_add_advertisement_configs_delete_menu(client, query)
        return
    elif query.data == "add_ad_config_info":
        await show_add_ad_config_info(client, query)
        return
    
    # Handle deletion callbacks
    if query.data.startswith("delete_listen_forward_"):
        chat_id = int(query.data.split("_")[-1])
        if _bot.remove_listen_forward_config(chat_id):
            # Stop the task if it's running
            if chat_id in _bot.listen_forward_chat:
                _bot.listen_forward_chat[chat_id].stop_transmission()
                del _bot.listen_forward_chat[chat_id]
            
            await client.answer_callback_query(query.id, f"{_t('Task deleted successfully')}")
            await show_listen_forward_delete_menu(client, query)
        else:
            await client.answer_callback_query(query.id, f"{_t('Task not found')}", show_alert=True)
        return
    
    elif query.data.startswith("delete_filter_ad_"):
        index = int(query.data.split("_")[-1])
        if 0 <= index < len(_bot.app.filter_advertisement_list):
            removed_filter = _bot.app.filter_advertisement_list.pop(index)
            _bot.app.update_config(True)
            await client.answer_callback_query(query.id, f"{_t('Filter deleted')}: {removed_filter[:20]}...")
            await show_advertisement_filters_delete_menu(client, query)
        else:
            await client.answer_callback_query(query.id, f"{_t('Filter not found')}", show_alert=True)
        return
    
    elif query.data.startswith("delete_replace_ad_"):
        index = int(query.data.split("_")[-1])
        if 0 <= index < len(_bot.app.replace_advertisement_list):
            removed_filter = _bot.app.replace_advertisement_list.pop(index)
            _bot.app.update_config(True)
            await client.answer_callback_query(query.id, f"{_t('Replace filter deleted')}: {removed_filter[:20]}...")
            await show_replace_filters_delete_menu(client, query)
        else:
            await client.answer_callback_query(query.id, f"{_t('Filter not found')}", show_alert=True)
        return
    
    elif query.data.startswith("delete_add_ad_"):
        chat_id = int(query.data.split("_")[-1])
        if chat_id in _bot.app.group_add_advertisement:
            del _bot.app.group_add_advertisement[chat_id]
            _bot.app.update_config(True)
            await client.answer_callback_query(query.id, f"{_t('Advertisement configuration deleted')}")
            await show_add_advertisement_configs_delete_menu(client, query)
        else:
            await client.answer_callback_query(query.id, f"{_t('Configuration not found')}", show_alert=True)
        return
    
    # Handle add regex filter callbacks
    elif query.data.startswith("add_regex_"):
        try:
            if query.data.startswith("add_regex_hash_"):
                # Handle hashed patterns
                pattern_hash = query.data.split("add_regex_hash_")[1]
                temp_patterns = getattr(_bot, 'temp_regex_patterns', {})
                if pattern_hash in temp_patterns:
                    regex_pattern = temp_patterns[pattern_hash]
                    # Clean up temporary storage
                    del temp_patterns[pattern_hash]
                else:
                    await client.answer_callback_query(query.id, f"❌ {_t('Pattern expired, please test again')}", show_alert=True)
                    return
            else:
                # Handle base64 encoded patterns
                encoded_pattern = query.data.split("add_regex_")[1]
                regex_pattern = base64.b64decode(encoded_pattern.encode('ascii')).decode('utf-8')
            
            # Validate regex pattern again
            try:
                re.compile(regex_pattern)
            except re.error as e:
                await client.answer_callback_query(query.id, f"❌ {_t('Invalid regex pattern')}: {str(e)}", show_alert=True)
                return
            
            # Check if pattern already exists
            if regex_pattern in _bot.app.replace_advertisement_list:
                await client.answer_callback_query(query.id, f"ℹ️ {_t('Pattern already exists in filters')}", show_alert=True)
                return
            
            # Add the regex pattern
            _bot.app.replace_advertisement_list.append(regex_pattern)
            _bot.app.update_config(True)
            
            # Update the message to show success
            await client.edit_message_text(
                query.message.chat.id,
                query.message.id,
                f"{query.message.text}\n\n✅ **{_t('Regex filter added successfully!')}**\n\n"
                f"📝 **{_t('Added Pattern')}**: `{regex_pattern}`\n\n"
                f"💡 {_t('You can manage all filters using /manage_tasks')}",
                parse_mode=pyrogram.enums.ParseMode.MARKDOWN
            )
            
            await client.answer_callback_query(query.id, f"✅ {_t('Regex filter added successfully!')}")
            
        except Exception as e:
            logger.exception(f"Error adding regex filter from callback: {e}")
            await client.answer_callback_query(query.id, f"❌ {_t('Error adding filter')}: {str(e)}", show_alert=True)
        return

    # Handle existing stop task callbacks
    for it in QueryHandler:
        queryHandler = QueryHandlerStr.get_str(it.value)
        if queryHandler in query.data:
            await stop_task(client, query, queryHandler, TaskType(it.value))


async def forward_to_comments(client: pyrogram.Client, message: pyrogram.types.Message):
    """
    Forwards specified media to a designated comment section.

    Usage: /forward_to_comments <source_chat_link> <destination_chat_link> <msg_start_id> <msg_end_id>

    Parameters:
        client (pyrogram.Client): The pyrogram client.
        message (pyrogram.types.Message): The message containing the command.
    """
    return await forward_message_impl(client, message, True)


async def check_regex_filter(client: pyrogram.Client, message: pyrogram.types.Message):
    """
    Check regex filter effectiveness on a specific message.
    
    Usage: /check_regex <message_link> <regex_pattern>
    
    Parameters:
        client (pyrogram.Client): The pyrogram client.
        message (pyrogram.types.Message): The message containing the command.
    
    Returns:
        None
    """
    
    args = message.text.split(maxsplit=2)
    if len(args) != 3:
        await client.send_message(
            message.from_user.id,
            f"❌ {_t('Invalid command format')}\n\n"
            f"📝 {_t('Usage')}: `/check_regex <message_link> <regex_pattern>`\n\n"
            f"📌 {_t('Example')}:\n"
            f"`/check_regex https://t.me/channel/123 广告.*`\n\n"
            f"💡 {_t('This command helps you test regex patterns before adding them as filters.')}",
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return
    
    message_link = args[1]
    regex_pattern = args[2]
    
    # Validate regex pattern first
    try:
        re.compile(regex_pattern)
    except re.error as e:
        await client.send_message(
            message.from_user.id,
            f"❌ {_t('Invalid regex pattern')}: `{regex_pattern}`\n\n"
            f"🔍 {_t('Error')}: {str(e)}\n\n"
            f"💡 {_t('Please check your regex syntax and try again.')}",
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return
    
    try:
        # Parse message link
        chat_id, message_id, _ = await parse_link(_bot.client, message_link)
        if not chat_id or not message_id:
            await client.send_message(
                message.from_user.id,
                f"❌ {_t('Invalid message link')}: `{message_link}`\n\n"
                f"💡 {_t('Please provide a valid Telegram message link.')}",
                parse_mode=pyrogram.enums.ParseMode.MARKDOWN
            )
            return
        
        # Get the message
        target_message = await retry(_bot.client.get_messages, args=(chat_id, message_id))
        if not target_message:
            await client.send_message(
                message.from_user.id,
                f"❌ {_t('Failed to retrieve message')}\n\n"
                f"🔍 {_t('Message ID')}: {message_id}\n"
                f"💡 {_t('Make sure the bot has access to the source chat.')}",
                parse_mode=pyrogram.enums.ParseMode.MARKDOWN
            )
            return
        
        # Extract text content
        original_text = ""
        if target_message.caption:
            original_text = target_message.caption
        elif target_message.text:
            original_text = target_message.text
        else:
            await client.send_message(
                message.from_user.id,
                f"ℹ️ {_t('Message has no text content to filter')}\n\n"
                f"💡 {_t('This message contains only media without caption or text.')}",
                parse_mode=pyrogram.enums.ParseMode.MARKDOWN
            )
            return
        
        # Apply regex filter
        filtered_text = re.sub(regex_pattern, "", original_text)
        
        # Prepare result message
        # Truncate texts if they're too long for display
        max_length = 4000
        
        original_display = original_text
        if len(original_text) > max_length:
            original_display = original_text[:max_length] + "..."
            
        filtered_display = filtered_text
        if len(filtered_text) > max_length:
            filtered_display = filtered_text[:max_length] + "..."
        
        # Check if there was any change
        has_changes = original_text != filtered_text
        status_emoji = "✅" if has_changes else "ℹ️"
        status_text = _t("Pattern matched - text was modified") if has_changes else _t("Pattern did not match - no changes")
        
        # Count matches
        matches = re.findall(regex_pattern, original_text)
        match_count = len(matches)
        
        result_message = (
            f"🔍 **{_t('Regex Filter Test Results')}**\n\n"
            f"📝 **{_t('Regex Pattern')}**: `{regex_pattern}`\n"
            f"{status_emoji} **{_t('Status')}**: {status_text}\n"
            f"🎯 **{_t('Matches Found')}**: {match_count}\n\n"
            #f"📄 **{_t('Original Text')}**:\n"
            #f"```\n{original_display}\n```\n\n"
            f"📋 **{_t('Filtered Text')}**:\n"
            f"\n{filtered_display or '[' + _t('Empty after filtering') + ']'}\n"
        )
        
        # Add matched text examples if there are matches
        if matches:
            result_message += f"\n\n🎯 **{_t('Matched Content')}**:\n"
            for i, match in enumerate(matches[:3], 1):  # Show first 3 matches
                match_display = match[:100] + "..." if len(match) > 100 else match
                result_message += f"{i}. `{match_display}`\n"
            if len(matches) > 3:
                result_message += f"... {_t('and')} {len(matches) - 3} {_t('more matches')}\n"
        
        # Create inline keyboard if there were matches
        reply_markup = None
        if has_changes and matches:
            # Encode regex pattern for callback data (limit is 64 bytes)
            
            # Create a hash if pattern is too long
            if len(regex_pattern.encode('utf-8')) > 40:  # Leave space for prefix
                pattern_hash = hashlib.md5(regex_pattern.encode('utf-8')).hexdigest()[:8]
                # Store the full pattern temporarily (in real implementation you'd use a proper storage)
                _bot.temp_regex_patterns = getattr(_bot, 'temp_regex_patterns', {})
                _bot.temp_regex_patterns[pattern_hash] = regex_pattern
                callback_data = f"add_regex_hash_{pattern_hash}"
            else:
                # Encode the pattern in base64 for shorter patterns
                encoded_pattern = base64.b64encode(regex_pattern.encode('utf-8')).decode('ascii')
                callback_data = f"add_regex_{encoded_pattern}"
            
            reply_markup = InlineKeyboardMarkup([
                [InlineKeyboardButton(
                    f"➕ {_t('Add this regex filter')}",
                    callback_data=callback_data
                )]
            ])
        
        await client.send_message(
            message.from_user.id,
            result_message,
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
        
    except Exception as e:
        logger.exception(f"Error in check_regex_filter: {e}")
        await client.send_message(
            message.from_user.id,
            f"❌ {_t('Error processing request')}\n\n"
            f"🔍 {_t('Error')}: {str(e)}\n\n"
            f"💡 {_t('Please check the message link and try again.')}",
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )


async def get_media_command(client: pyrogram.Client, message: pyrogram.types.Message):
    """
    Get media from a message link, including media groups and discussion media.
    
    Usage: /get_media <message_link>
    
    Parameters:
        client (pyrogram.Client): The pyrogram client.
        message (pyrogram.types.Message): The message containing the command.
    
    Returns:
        None
    """
    
    args = message.text.split(maxsplit=1)
    if len(args) != 2:
        await client.send_message(
            message.from_user.id,
            f"❌ {_t('Invalid command format')}\n\n"
            f"📝 {_t('Usage')}: `/get_media <message_link>`\n\n"
            f"📌 {_t('Example')}:\n"
            f"`/get_media https://t.me/channel/123`\n"
            f"`/get_media https://t.me/channel/123?comment=456`\n\n"
            f"💡 {_t('This command gets media from the message and its discussion if available.')}",
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
        return
    
    message_link = args[1]
    
    try:
        # Parse message link
        chat_id, message_id, topic_id = await parse_link(_bot.client, message_link)
        if not chat_id or not message_id:
            await client.send_message(
                message.from_user.id,
                f"❌ {_t('Invalid message link')}: `{message_link}`\n\n"
                f"💡 {_t('Please provide a valid Telegram message link.')}",
                parse_mode=pyrogram.enums.ParseMode.MARKDOWN
            )
            return
        
        # Send processing message
        processing_msg = await client.send_message(
            message.from_user.id,
            f"🔄 {_t('Processing message, please wait...')}",
            reply_to_message_id=message.id
        )
        
        # Get the target message and source chat info
        target_message = await retry(_bot.client.get_messages, args=(chat_id, message_id))
        if not target_message:
            await client.edit_message_text(
                message.from_user.id,
                processing_msg.id,
                f"❌ {_t('Failed to retrieve message')}\n\n"
                f"🔍 {_t('Message ID')}: {message_id}\n"
                f"💡 {_t('Make sure the bot has access to the source chat.')}"
            )
            return
        
        # Check if source chat has protected content
        source_chat = await _bot.client.get_chat(chat_id)
        has_protected_content = source_chat.has_protected_content
        
        messages_to_process = []
        
        # Check if target message is part of a media group
        if target_message.media_group_id:
            try:
                media_group = await get_media_group_with_retry(
                    _bot.client, chat_id, message_id, 5
                )
                messages_to_process.extend(media_group)
                await client.edit_message_text(
                    message.from_user.id,
                    processing_msg.id,
                    f"✅ {_t('Found media group with')} {len(media_group)} {_t('items')}\n"
                    f"🔄 {_t('Checking for discussion messages...')}"
                )
            except Exception as e:
                logger.warning(f"Failed to get media group: {e}")
                messages_to_process.append(target_message)
        else:
            # Single message
            messages_to_process.append(target_message)
        
        # Check for discussion/comments
        discussion_messages = []
        try:
            # Check if the chat has discussion enabled
            if hasattr(source_chat, 'linked_chat') and source_chat.linked_chat:
                # Get discussion message
                discussion_msg = await _bot.client.get_discussion_message(chat_id, message_id)
                if discussion_msg:
                    # Get messages from the discussion thread
                    discussion_chat_id = source_chat.linked_chat.id
                    discussion_message_id = discussion_msg.id
                    
                    # Get recent messages from the discussion thread
                    async for disc_msg in get_chat_history_v2(
                        _bot.client, 
                        discussion_chat_id, 
                        limit=100,
                        offset_id=discussion_message_id,
                        reverse=True
                    ):
                        # Only include messages with media
                        if disc_msg.media and getattr(disc_msg, disc_msg.media.value):
                            discussion_messages.append(disc_msg)
                    
                    if discussion_messages:
                        await client.edit_message_text(
                            message.from_user.id,
                            processing_msg.id,
                            f"✅ {_t('Found')} {len(messages_to_process)} {_t('main messages')}\n"
                            f"✅ {_t('Found')} {len(discussion_messages)} {_t('discussion media')}\n"
                            f"🔄 {_t('Processing content...')}"
                        )
        except Exception as e:
            logger.warning(f"Failed to get discussion messages: {e}")
        
        # Filter messages with media
        main_media_messages = [msg for msg in messages_to_process if msg.media and getattr(msg, msg.media.value)]
        total_messages = len(main_media_messages) + len(discussion_messages)
        
        if total_messages == 0:
            await client.edit_message_text(
                message.from_user.id,
                processing_msg.id,
                f"ℹ️ {_t('No media found in the specified message.')}"
            )
            return
        
        # Determine processing method based on content protection
        if has_protected_content:
            # Download protected content
            await client.edit_message_text(
                message.from_user.id,
                processing_msg.id,
                f"🔒 {_t('Content is protected, downloading')} {total_messages} {_t('media items...')}"
            )
            
            # Download main messages
            for msg in main_media_messages:
                await direct_download(_bot, chat_id, message, msg, _bot.client)
            
            # Download discussion messages
            for disc_msg in discussion_messages:
                await direct_download(_bot, source_chat.linked_chat.id, message, disc_msg, _bot.client)
            
            method_text = f"📥 {_t('Downloaded')}"
        else:
            # Forward non-protected content
            await client.edit_message_text(
                message.from_user.id,
                processing_msg.id,
                f"📤 {_t('Forwarding')} {total_messages} {_t('media items...')}"
            )
            
            forwarded_count = 0
            failed_count = 0
            
            # Forward main messages (batch forward to maintain media group structure)
            if main_media_messages:
                try:
                    # Group messages by media_group_id to maintain group structure
                    media_groups = {}
                    single_messages = []
                    
                    for msg in main_media_messages:
                        if msg.media_group_id:
                            if msg.media_group_id not in media_groups:
                                media_groups[msg.media_group_id] = []
                            media_groups[msg.media_group_id].append(msg)
                        else:
                            single_messages.append(msg)
                    
                    # Forward media groups as batches
                    for group_id, group_messages in media_groups.items():
                        try:
                            message_ids = [msg.id for msg in group_messages]
                            await _bot.client.forward_messages(
                                chat_id=message.from_user.id,
                                from_chat_id=chat_id,
                                message_ids=message_ids
                            )
                            forwarded_count += len(group_messages)
                        except Exception as e:
                            logger.warning(f"Failed to forward media group {group_id}: {e}")
                            # Fallback to download for failed media group
                            for msg in group_messages:
                                await direct_download(_bot, chat_id, message, msg, _bot.client)
                                failed_count += 1
                    
                    # Forward single messages
                    for msg in single_messages:
                        try:
                            await _bot.client.forward_messages(
                                chat_id=message.from_user.id,
                                from_chat_id=chat_id,
                                message_ids=msg.id
                            )
                            forwarded_count += 1
                        except Exception as e:
                            logger.warning(f"Failed to forward message {msg.id}: {e}")
                            # Fallback to download if forward fails
                            await direct_download(_bot, chat_id, message, msg, _bot.client)
                            failed_count += 1
                            
                except Exception as e:
                    logger.exception(f"Error in main messages forwarding: {e}")
                    # Fallback to download all main messages
                    for msg in main_media_messages:
                        await direct_download(_bot, chat_id, message, msg, _bot.client)
                        failed_count += 1
            
            # Forward discussion messages (can be batched for better performance)
            if discussion_messages:
                try:
                    # Group discussion messages by media_group_id as well
                    disc_media_groups = {}
                    disc_single_messages = []
                    
                    for disc_msg in discussion_messages:
                        if disc_msg.media_group_id:
                            if disc_msg.media_group_id not in disc_media_groups:
                                disc_media_groups[disc_msg.media_group_id] = []
                            disc_media_groups[disc_msg.media_group_id].append(disc_msg)
                        else:
                            disc_single_messages.append(disc_msg)
                    
                    # Forward discussion media groups as batches
                    for group_id, group_messages in disc_media_groups.items():
                        try:
                            message_ids = [msg.id for msg in group_messages]
                            await _bot.client.forward_messages(
                                chat_id=message.from_user.id,
                                from_chat_id=source_chat.linked_chat.id,
                                message_ids=message_ids
                            )
                            forwarded_count += len(group_messages)
                        except Exception as e:
                            logger.warning(f"Failed to forward discussion media group {group_id}: {e}")
                            # Fallback to download for failed discussion media group
                            # for msg in group_messages:
                            #     await direct_download(_bot, source_chat.linked_chat.id, message, msg, _bot.client)
                            #     failed_count += 1
                    
                    # Forward single discussion messages
                    for disc_msg in disc_single_messages:
                        try:
                            await _bot.client.forward_messages(
                                chat_id=message.from_user.id,
                                from_chat_id=source_chat.linked_chat.id,
                                message_ids=disc_msg.id
                            )
                            forwarded_count += 1
                        except Exception as e:
                            logger.warning(f"Failed to forward discussion message {disc_msg.id}: {e}")
                            # Fallback to download if forward fails
                            await direct_download(_bot, source_chat.linked_chat.id, message, disc_msg, _bot.client)
                            failed_count += 1
                            
                except Exception as e:
                    logger.exception(f"Error in discussion messages forwarding: {e}")
                    # Fallback to download all discussion messages
                    for disc_msg in discussion_messages:
                        await direct_download(_bot, source_chat.linked_chat.id, message, disc_msg, _bot.client)
                        failed_count += 1
            
            # Determine method text based on success/failure
            if failed_count == 0:
                method_text = f"📤 {_t('Forwarded')}"
            elif forwarded_count == 0:
                method_text = f"📥 {_t('Downloaded')} ({_t('forward failed')})"
            else:
                method_text = f"📤 {_t('Forwarded')} ({forwarded_count}) + 📥 {_t('Downloaded')} ({failed_count})"
        
        # Final status update
        await client.edit_message_text(
            message.from_user.id,
            processing_msg.id,
            f"✅ {_t('Successfully processed')} {total_messages} {_t('media items')}\n"
            f"📊 **{_t('Summary')}:**\n"
            f"📥 {_t('Main messages')}: {len(main_media_messages)}\n"
            f"💬 {_t('Discussion messages')}: {len(discussion_messages)}\n"
            f"🔧 {_t('Method')}: {method_text}\n"
            f"🔒 {_t('Protected content')}: {'✅' if has_protected_content else '❌'}"
        )
        
    except Exception as e:
        logger.exception(f"Error in get_media_command: {e}")
        await client.send_message(
            message.from_user.id,
            f"❌ {_t('Error processing request')}\n\n"
            f"🔍 {_t('Error')}: {str(e)}\n\n"
            f"💡 {_t('Please check the message link and try again.')}",
            parse_mode=pyrogram.enums.ParseMode.MARKDOWN
        )
