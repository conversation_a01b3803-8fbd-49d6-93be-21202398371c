import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useTaskStore } from '../taskStore'
import { mockTask } from '@/test/utils'

// Mock fetch globally
global.fetch = vi.fn()

describe('taskStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useTaskStore.setState({
      tasks: [],
      activeTasks: [],
      isLoading: false,
      error: null,
    })
    vi.clearAllMocks()
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = useTaskStore.getState()
      
      expect(state.tasks).toEqual([])
      expect(state.activeTasks).toEqual([])
      expect(state.isLoading).toBe(false)
      expect(state.error).toBe(null)
    })
  })

  describe('fetchTasks', () => {
    it('should fetch tasks successfully', async () => {
      const mockTasks = [
        mockTask({ id: '1', type: 'download' }),
        mockTask({ id: '2', type: 'forward' }),
      ]

      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockTasks }),
      })

      const { fetchTasks } = useTaskStore.getState()
      await fetchTasks()

      const state = useTaskStore.getState()
      expect(state.tasks).toEqual(mockTasks)
      expect(state.isLoading).toBe(false)
      expect(state.error).toBe(null)
    })

    it('should handle fetch error', async () => {
      ;(global.fetch as any).mockRejectedValueOnce(new Error('Network error'))

      const { fetchTasks } = useTaskStore.getState()
      await fetchTasks()

      const state = useTaskStore.getState()
      expect(state.isLoading).toBe(false)
      expect(state.error).toBe('获取任务列表失败')
    })

    it('should set loading state during fetch', async () => {
      let resolvePromise: (value: any) => void
      const promise = new Promise(resolve => {
        resolvePromise = resolve
      })

      ;(global.fetch as any).mockReturnValueOnce(promise)

      const { fetchTasks } = useTaskStore.getState()
      const fetchPromise = fetchTasks()

      // Check loading state
      expect(useTaskStore.getState().isLoading).toBe(true)

      // Resolve the promise
      resolvePromise!({
        ok: true,
        json: async () => ({ data: [] }),
      })

      await fetchPromise

      // Check final state
      expect(useTaskStore.getState().isLoading).toBe(false)
    })
  })

  describe('createTask', () => {
    it('should create task successfully', async () => {
      const newTaskData = {
        type: 'download',
        accountId: 'account-1',
        sourceGroupId: 'group-1',
      }

      const createdTask = mockTask(newTaskData)

      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: createdTask }),
      })

      const { createTask } = useTaskStore.getState()
      await createTask(newTaskData)

      const state = useTaskStore.getState()
      expect(state.tasks).toContain(createdTask)
    })

    it('should handle create task error', async () => {
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: false,
      })

      const { createTask } = useTaskStore.getState()
      
      await expect(createTask({})).rejects.toThrow()
      
      const state = useTaskStore.getState()
      expect(state.error).toBe('创建任务失败')
    })
  })

  describe('updateTask', () => {
    it('should update existing task', async () => {
      const existingTask = mockTask({ id: 'task-1', status: 'pending' })
      useTaskStore.setState({ tasks: [existingTask] })

      const updates = { status: 'running' as const }
      const updatedTask = { ...existingTask, ...updates }

      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: updatedTask }),
      })

      const { updateTask } = useTaskStore.getState()
      await updateTask('task-1', updates)

      const state = useTaskStore.getState()
      const task = state.tasks.find(t => t.id === 'task-1')
      expect(task?.status).toBe('running')
    })

    it('should handle update non-existent task', async () => {
      const { updateTask } = useTaskStore.getState()
      
      await expect(updateTask('non-existent', {})).rejects.toThrow()
      
      const state = useTaskStore.getState()
      expect(state.error).toBe('任务不存在')
    })
  })

  describe('deleteTask', () => {
    it('should delete existing task', async () => {
      const task = mockTask({ id: 'task-1' })
      useTaskStore.setState({ tasks: [task] })

      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({}),
      })

      const { deleteTask } = useTaskStore.getState()
      await deleteTask('task-1')

      const state = useTaskStore.getState()
      expect(state.tasks).not.toContain(task)
    })
  })

  describe('task filtering', () => {
    beforeEach(() => {
      const tasks = [
        mockTask({ id: '1', type: 'download', status: 'running' }),
        mockTask({ id: '2', type: 'forward', status: 'completed' }),
        mockTask({ id: '3', type: 'download', status: 'failed' }),
      ]
      useTaskStore.setState({ tasks })
    })

    it('should filter tasks by type', () => {
      const { getTasksByType } = useTaskStore.getState()
      
      const downloadTasks = getTasksByType('download')
      expect(downloadTasks).toHaveLength(2)
      expect(downloadTasks.every(t => t.type === 'download')).toBe(true)
    })

    it('should filter tasks by status', () => {
      const { getTasksByStatus } = useTaskStore.getState()
      
      const runningTasks = getTasksByStatus('running')
      expect(runningTasks).toHaveLength(1)
      expect(runningTasks[0].status).toBe('running')
    })
  })

  describe('updateTaskProgress', () => {
    it('should update task progress', () => {
      const task = mockTask({ id: 'task-1', progress: 0 })
      useTaskStore.setState({ tasks: [task] })

      const { updateTaskProgress } = useTaskStore.getState()
      updateTaskProgress('task-1', 50)

      const state = useTaskStore.getState()
      const updatedTask = state.tasks.find(t => t.id === 'task-1')
      expect(updatedTask?.progress).toBe(50)
    })
  })
})
