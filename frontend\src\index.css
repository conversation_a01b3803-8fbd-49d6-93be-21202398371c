/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--ant-color-text);
  background-color: var(--ant-color-bg-layout);
  transition: background-color 0.3s ease, color 0.3s ease;
}

#root {
  min-height: 100vh;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 暗色模式滚动条 */
[data-theme='dark'] ::-webkit-scrollbar-track {
  background: #2f2f2f;
}

[data-theme='dark'] ::-webkit-scrollbar-thumb {
  background: #6c6c6c;
}

[data-theme='dark'] ::-webkit-scrollbar-thumb:hover {
  background: #8c8c8c;
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* 确保下拉菜单在最顶层 */
.ant-dropdown {
  z-index: 9999 !important;
}

.ant-dropdown-menu {
  z-index: 9999 !important;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
}

.ant-dropdown-menu-item {
  pointer-events: auto !important;
  cursor: pointer !important;
}

.ant-dropdown-menu-item:hover {
  background-color: var(--ant-color-bg-elevated) !important;
}

/* 暗色模式下拉菜单项悬停效果 */
[data-theme='dark'] .ant-dropdown-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
}

/* 确保用户菜单下拉框可点击 */
.ant-dropdown-trigger {
  z-index: 1000;
  pointer-events: auto !important;
}

/* 确保菜单项的图标和文字都可以点击 */
.ant-dropdown-menu-item .anticon,
.ant-dropdown-menu-item span {
  pointer-events: none !important;
}

/* 防止其他元素覆盖下拉菜单 */
.ant-layout-header {
  position: relative;
  z-index: 100;
}

/* 确保下拉菜单容器的点击事件正常 */
.ant-dropdown-menu-root {
  pointer-events: auto !important;
}
