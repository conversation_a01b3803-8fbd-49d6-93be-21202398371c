"""
账号管理API路由
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..auth import get_current_active_user, User
from ..database import get_db, TelegramAccount
from ..utils import create_response

router = APIRouter()


class AccountResponse(BaseModel):
    """账号响应模型"""
    id: str
    name: str
    phone: str
    isActive: bool
    lastLogin: Optional[str] = None


class AccountCreateRequest(BaseModel):
    """创建账号请求模型"""
    name: str
    phone: str
    api_id: Optional[int] = None
    api_hash: Optional[str] = None


@router.get("")
async def get_accounts(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取账号列表"""
    try:
        # 查询所有活跃账号
        result = await db.execute(
            select(TelegramAccount).where(TelegramAccount.is_active == True)
        )
        accounts = result.scalars().all()
        
        # 转换为响应格式
        account_list = []
        for account in accounts:
            account_data = AccountResponse(
                id=str(account.id),
                name=account.name,
                phone=account.phone,
                isActive=account.is_active,
                lastLogin=account.last_login.isoformat() + "Z" if account.last_login else None
            )
            account_list.append(account_data.dict())
        
        return create_response(
            success=True,
            data=account_list
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取账号列表失败: {str(e)}"
        )


@router.post("")
async def create_account(
    account_data: AccountCreateRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """创建新账号"""
    try:
        # 检查手机号是否已存在
        result = await db.execute(
            select(TelegramAccount).where(TelegramAccount.phone == account_data.phone)
        )
        existing_account = result.scalar_one_or_none()
        
        if existing_account:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该手机号已存在"
            )
        
        # 创建新账号
        new_account = TelegramAccount(
            name=account_data.name,
            phone=account_data.phone,
            api_id=account_data.api_id,
            api_hash=account_data.api_hash
        )
        
        db.add(new_account)
        await db.commit()
        await db.refresh(new_account)
        
        # 返回创建的账号信息
        account_response = AccountResponse(
            id=str(new_account.id),
            name=new_account.name,
            phone=new_account.phone,
            isActive=new_account.is_active
        )
        
        return create_response(
            success=True,
            data=account_response.dict(),
            message="账号创建成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建账号失败: {str(e)}"
        )


@router.get("/{account_id}")
async def get_account(
    account_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取单个账号信息"""
    try:
        result = await db.execute(
            select(TelegramAccount).where(TelegramAccount.id == account_id)
        )
        account = result.scalar_one_or_none()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在"
            )
        
        account_response = AccountResponse(
            id=str(account.id),
            name=account.name,
            phone=account.phone,
            isActive=account.is_active,
            lastLogin=account.last_login.isoformat() + "Z" if account.last_login else None
        )
        
        return create_response(
            success=True,
            data=account_response.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取账号信息失败: {str(e)}"
        )


@router.delete("/{account_id}")
async def delete_account(
    account_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """删除账号"""
    try:
        result = await db.execute(
            select(TelegramAccount).where(TelegramAccount.id == account_id)
        )
        account = result.scalar_one_or_none()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在"
            )
        
        # 软删除 - 设置为非活跃状态
        account.is_active = False
        await db.commit()
        
        return create_response(
            success=True,
            message="账号删除成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除账号失败: {str(e)}"
        )
