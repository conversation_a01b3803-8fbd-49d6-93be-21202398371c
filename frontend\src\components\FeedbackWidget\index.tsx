import React, { useState } from 'react';
import {
  FloatButton,
  Modal,
  Form,
  Input,
  Rate,
  Select,
  Button,
  Space,
  Typography,
  message,
  Divider,
} from 'antd';
import {
  MessageOutlined,
  BugOutlined,
  BulbOutlined,
  HeartOutlined,
  SendOutlined,
} from '@ant-design/icons';

const { TextArea } = Input;
const { Text } = Typography;
const { Option } = Select;

interface FeedbackData {
  type: 'bug' | 'feature' | 'general' | 'praise';
  rating: number;
  title: string;
  description: string;
  email?: string;
  page: string;
  userAgent: string;
  timestamp: string;
}

const FeedbackWidget: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const feedbackTypes = [
    { value: 'bug', label: '报告问题', icon: <BugOutlined />, color: '#ff4d4f' },
    { value: 'feature', label: '功能建议', icon: <BulbOutlined />, color: '#1890ff' },
    { value: 'general', label: '一般反馈', icon: <MessageOutlined />, color: '#52c41a' },
    { value: 'praise', label: '表扬鼓励', icon: <HeartOutlined />, color: '#eb2f96' },
  ];

  const handleSubmit = async (values: any) => {
    setLoading(true);
    
    try {
      const feedbackData: FeedbackData = {
        ...values,
        page: window.location.pathname,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
      };

      // TODO: 发送反馈到后端
      console.log('Feedback submitted:', feedbackData);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('感谢您的反馈！我们会认真考虑您的建议。');
      setVisible(false);
      form.resetFields();
      
      // 发送到分析服务
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', 'feedback_submitted', {
          feedback_type: values.type,
          rating: values.rating,
          page: window.location.pathname,
        });
      }
      
    } catch (error) {
      message.error('提交失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setVisible(false);
    form.resetFields();
  };

  const getTypeConfig = (type: string) => {
    return feedbackTypes.find(t => t.value === type) || feedbackTypes[0];
  };

  return (
    <>
      <FloatButton
        icon={<MessageOutlined />}
        tooltip="意见反馈"
        onClick={() => setVisible(true)}
        style={{
          right: 24,
          bottom: 24,
        }}
      />

      <Modal
        title={
          <Space>
            <MessageOutlined />
            意见反馈
          </Space>
        }
        open={visible}
        onCancel={handleCancel}
        footer={null}
        width={600}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            type: 'general',
            rating: 5,
          }}
        >
          <Form.Item
            name="type"
            label="反馈类型"
            rules={[{ required: true, message: '请选择反馈类型' }]}
          >
            <Select>
              {feedbackTypes.map(type => (
                <Option key={type.value} value={type.value}>
                  <Space>
                    <span style={{ color: type.color }}>{type.icon}</span>
                    {type.label}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="rating"
            label="整体评分"
            rules={[{ required: true, message: '请给出评分' }]}
          >
            <Rate allowHalf />
          </Form.Item>

          <Form.Item
            name="title"
            label="标题"
            rules={[
              { required: true, message: '请输入标题' },
              { max: 100, message: '标题不能超过100个字符' },
            ]}
          >
            <Input placeholder="简要描述您的反馈" />
          </Form.Item>

          <Form.Item
            name="description"
            label="详细描述"
            rules={[
              { required: true, message: '请输入详细描述' },
              { min: 10, message: '描述至少需要10个字符' },
              { max: 1000, message: '描述不能超过1000个字符' },
            ]}
          >
            <TextArea
              rows={4}
              placeholder="请详细描述您遇到的问题、建议或想法..."
              showCount
              maxLength={1000}
            />
          </Form.Item>

          <Form.Item
            name="email"
            label="联系邮箱（可选）"
            rules={[
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input placeholder="如需回复，请留下您的邮箱" />
          </Form.Item>

          <Divider />

          <div style={{ marginBottom: 16 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              当前页面: {window.location.pathname}
            </Text>
          </div>

          <Form.Item style={{ marginBottom: 0 }}>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={handleCancel}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<SendOutlined />}
              >
                提交反馈
              </Button>
            </Space>
          </Form.Item>
        </Form>

        <div style={{ marginTop: 16, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            💡 提示：您的反馈对我们非常重要，我们会认真阅读每一条反馈并持续改进产品体验。
          </Text>
        </div>
      </Modal>
    </>
  );
};

export default FeedbackWidget;
