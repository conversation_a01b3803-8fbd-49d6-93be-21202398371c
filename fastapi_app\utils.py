"""
FastAPI工具模块
"""

import logging
import sys
from datetime import datetime
from typing import Any, Dict, Optional
from pathlib import Path


def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """设置日志配置"""
    # 配置日志格式
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 配置根日志器
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)


def create_response(
    success: bool = True,
    data: Any = None,
    message: str = "",
    error: str = "",
    code: str = "",
    timestamp: Optional[str] = None
) -> Dict[str, Any]:
    """创建标准API响应格式"""
    response = {
        "success": success,
        "timestamp": timestamp or datetime.utcnow().isoformat() + "Z"
    }
    
    if data is not None:
        response["data"] = data
    
    if message:
        response["message"] = message
    
    if error:
        response["error"] = error
    
    if code:
        response["code"] = code
    
    return response


def format_bytes(bytes_value: int) -> str:
    """格式化字节数为人类可读格式"""
    if bytes_value == 0:
        return "0 B"
    
    units = ["B", "KB", "MB", "GB", "TB", "PB"]
    unit_index = 0
    size = float(bytes_value)
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    if unit_index == 0:
        return f"{int(size)} {units[unit_index]}"
    else:
        return f"{size:.2f} {units[unit_index]}"


def format_duration(seconds: int) -> str:
    """格式化秒数为人类可读的时间格式"""
    if seconds < 60:
        return f"{seconds} 秒"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        if remaining_seconds == 0:
            return f"{minutes} 分钟"
        else:
            return f"{minutes} 分钟 {remaining_seconds} 秒"
    elif seconds < 86400:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        if remaining_minutes == 0:
            return f"{hours} 小时"
        else:
            return f"{hours} 小时 {remaining_minutes} 分钟"
    else:
        days = seconds // 86400
        remaining_hours = (seconds % 86400) // 3600
        if remaining_hours == 0:
            return f"{days} 天"
        else:
            return f"{days} 天 {remaining_hours} 小时"


def validate_phone_number(phone: str) -> bool:
    """验证手机号格式"""
    import re
    # 简单的手机号验证（支持国际格式）
    pattern = r'^\+?[1-9]\d{1,14}$'
    return bool(re.match(pattern, phone))


def sanitize_filename(filename: str) -> str:
    """清理文件名，移除不安全字符"""
    import re
    # 移除或替换不安全的字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 移除控制字符
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    # 限制长度
    if len(filename) > 255:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        max_name_length = 255 - len(ext) - 1 if ext else 255
        filename = name[:max_name_length] + ('.' + ext if ext else '')
    
    return filename.strip()


def generate_task_id() -> str:
    """生成任务ID"""
    import uuid
    return str(uuid.uuid4())


def get_file_extension(filename: str) -> str:
    """获取文件扩展名"""
    return Path(filename).suffix.lower()


def is_media_file(filename: str) -> bool:
    """判断是否为媒体文件"""
    media_extensions = {
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',  # 图片
        '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm',  # 视频
        '.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a',  # 音频
    }
    return get_file_extension(filename) in media_extensions


def calculate_progress(current: int, total: int) -> int:
    """计算进度百分比"""
    if total == 0:
        return 0
    return min(100, max(0, int((current / total) * 100)))


class AsyncContextManager:
    """异步上下文管理器基类"""
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass
