import React from 'react';
import { Card, Progress, Space, Typography, Tag, Button, Dropdown, Tooltip } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  MoreOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  SoundOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import type { Task, TaskStatus } from '@/types';
import { TASK_STATUS_COLORS } from '@/utils/constants';

const { Text, Title } = Typography;

interface TaskCardProps {
  task: Task;
  onStart?: (taskId: string) => void;
  onPause?: (taskId: string) => void;
  onStop?: (taskId: string) => void;
  onDelete?: (taskId: string) => void;
  loading?: boolean;
}

const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onStart,
  onPause,
  onStop,
  onDelete,
  loading = false,
}) => {
  const getTaskIcon = () => {
    switch (task.type) {
      case 'download':
        return <DownloadOutlined />;
      case 'forward':
        return <ShareAltOutlined />;
      case 'listen_forward':
        return <SoundOutlined />;
      default:
        return <DownloadOutlined />;
    }
  };

  const getTaskTypeText = () => {
    switch (task.type) {
      case 'download':
        return '下载任务';
      case 'forward':
        return '转发任务';
      case 'listen_forward':
        return '监听转发';
      default:
        return '未知任务';
    }
  };

  const getStatusText = (status: TaskStatus) => {
    const statusMap = {
      pending: '等待中',
      running: '运行中',
      paused: '已暂停',
      completed: '已完成',
      failed: '失败',
      cancelled: '已取消',
    };
    return statusMap[status] || '未知';
  };

  const canStart = task.status === 'pending' || task.status === 'paused';
  const canPause = task.status === 'running';
  const canStop = task.status === 'running' || task.status === 'paused';

  const menuItems: MenuProps['items'] = [
    {
      key: 'start',
      icon: <PlayCircleOutlined />,
      label: '开始',
      disabled: !canStart,
      onClick: () => onStart?.(task.id),
    },
    {
      key: 'pause',
      icon: <PauseCircleOutlined />,
      label: '暂停',
      disabled: !canPause,
      onClick: () => onPause?.(task.id),
    },
    {
      key: 'stop',
      icon: <StopOutlined />,
      label: '停止',
      disabled: !canStop,
      onClick: () => onStop?.(task.id),
    },
    {
      type: 'divider',
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: '删除',
      danger: true,
      onClick: () => onDelete?.(task.id),
    },
  ];

  return (
    <Card
      size="small"
      title={
        <Space>
          {getTaskIcon()}
          <Title level={5} style={{ margin: 0 }}>
            {getTaskTypeText()}
          </Title>
          <Tag color={TASK_STATUS_COLORS[task.status]}>
            {getStatusText(task.status)}
          </Tag>
        </Space>
      }
      extra={
        <Dropdown menu={{ items: menuItems }} trigger={['click']}>
          <Button type="text" icon={<MoreOutlined />} loading={loading} />
        </Dropdown>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 任务信息 */}
        <div>
          <Text type="secondary">任务ID: </Text>
          <Text code>{task.id.slice(0, 8)}...</Text>
        </div>
        
        {task.sourceGroupId && (
          <div>
            <Text type="secondary">源群组: </Text>
            <Text>{task.sourceGroupId}</Text>
          </div>
        )}
        
        {task.targetGroupId && (
          <div>
            <Text type="secondary">目标群组: </Text>
            <Text>{task.targetGroupId}</Text>
          </div>
        )}

        {/* 进度信息 */}
        {task.status === 'running' && (
          <div>
            <div style={{ marginBottom: 8 }}>
              <Text type="secondary">进度: </Text>
              <Text>{task.completedFiles}/{task.totalFiles}</Text>
              {task.downloadSpeed && (
                <Text type="secondary" style={{ marginLeft: 16 }}>
                  速度: {task.downloadSpeed}
                </Text>
              )}
            </div>
            <Progress
              percent={task.progress}
              size="small"
              status={task.status === 'failed' ? 'exception' : 'active'}
            />
          </div>
        )}

        {/* 错误信息 */}
        {task.error && (
          <div>
            <Text type="danger" style={{ fontSize: '12px' }}>
              错误: {task.error}
            </Text>
          </div>
        )}

        {/* 时间信息 */}
        <div style={{ fontSize: '12px', color: '#999' }}>
          <div>创建时间: {new Date(task.createdAt).toLocaleString()}</div>
          <div>更新时间: {new Date(task.updatedAt).toLocaleString()}</div>
        </div>
      </Space>
    </Card>
  );
};

export default TaskCard;
