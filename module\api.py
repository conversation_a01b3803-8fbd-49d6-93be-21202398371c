"""
API路由模块 - 为React前端提供RESTful API
"""

import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from flask import Blueprint, request, jsonify, session, current_app
from functools import wraps

try:
    from .database import (
        db, User, TelegramAccount, TelegramGroup, Task, ForwardRule,
        SystemSettings, TaskLog, get_setting, set_setting
    )
except ImportError:
    # 如果新的数据库模块不存在，使用模拟数据
    db = None
    User = None
    TelegramAccount = None
    TelegramGroup = None
    Task = None
    ForwardRule = None
    SystemSettings = None
    TaskLog = None
    get_setting = lambda k, d=None: d
    set_setting = lambda k, v, t='string', d='': None

# 创建蓝图
api_bp = Blueprint('api', __name__)

# 日志记录器
logger = logging.getLogger(__name__)

# 模拟数据存储（当数据库模块不可用时使用）
tasks_storage: Dict[str, dict] = {}
accounts_storage: List[dict] = [
    {
        'id': '1',
        'name': '主账号',
        'phone': '+86 138****1234',
        'isActive': True,
        'lastLogin': '2024-01-15T10:30:00.000Z',
    },
    {
        'id': '2',
        'name': '备用账号',
        'phone': '+86 139****5678',
        'isActive': False,
        'lastLogin': '2024-01-14T15:20:00.000Z',
    },
]

groups_storage: List[dict] = [
    {
        'id': 'group1',
        'title': '技术交流群',
        'type': 'supergroup',
        'memberCount': 1250,
        'description': '技术讨论和资源分享',
    },
    {
        'id': 'channel1',
        'title': '资源分享频道',
        'type': 'channel',
        'memberCount': 5680,
        'description': '各种学习资源和工具分享',
    },
    {
        'id': 'group2',
        'title': '项目协作群',
        'type': 'group',
        'memberCount': 45,
        'description': '项目开发协作讨论',
    },
]

settings_storage: dict = {
    'downloadPath': './downloads',
    'maxConcurrentTasks': 3,
    'theme': 'light',
    'language': 'zh-CN',
    'telegramSettings': {
        'apiId': 0,
        'apiHash': '',
        'sessionName': 'telegram_session',
        'botToken': '',
        'sessionPath': './sessions',
    },
    'fileSettings': {
        'maxFileSize': 2 * 1024 * 1024 * 1024,  # 2GB
        'chunkSize': 1024 * 1024,  # 1MB
        'filePathPrefix': ['chat_title', 'media_datetime'],
        'hideFileName': False,
        'fileNamePrefix': ['message_id', 'file_name'],
        'mediaTypes': ['audio', 'document', 'photo', 'video', 'voice', 'animation'],
        'fileFormats': {
            'audio': ['all'],
            'document': ['pdf', 'epub'],
            'video': ['mp4'],
            'photo': ['all'],
            'voice': ['all'],
            'animation': ['all']
        },
        'enableDownloadTxt': False,
        'dropNoAudioVideo': False,
    },
    'performanceSettings': {
        'timeout': 30,
        'retryAttempts': 3,
        'retryDelay': 5,
        'maxDownloadSpeed': 0,
        'maxConcurrentTransmissions': 1,
        'startTimeout': 60,
    },
    'securitySettings': {
        'sessionTimeout': 3600,
        'maxLoginAttempts': 5,
        'loginAttemptTimeout': 300,
        'allowedHosts': ['localhost', '127.0.0.1'],
        'webLoginSecret': '',
        'allowedUserIds': [],
    },
    'databaseSettings': {
        'url': 'sqlite:///app.db',
        'echo': False,
        'poolSize': 10,
        'maxOverflow': 20,
    },
    'proxySettings': {
        'enabled': False,
        'type': 'http',
        'host': '',
        'port': 0,
        'username': '',
        'password': '',
    },
    'notificationSettings': {
        'enabled': True,
        'taskCompletion': True,
        'taskFailure': True,
        'systemAlerts': True,
        'emailEnabled': False,
        'emailSmtpServer': '',
        'emailSmtpPort': 587,
        'emailUsername': '',
        'emailPassword': '',
        'emailFrom': '',
        'emailTo': [],
    },
}


def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return create_response(
                success=False,
                error='Authentication required'
            ), 401
        return f(*args, **kwargs)
    return decorated_function


def admin_required(f):
    """管理员权限验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return create_response(
                success=False,
                error='Authentication required'
            ), 401

        # 在模拟模式下，假设用户是管理员
        if not User:
            return f(*args, **kwargs)

        user = User.query.get(session['user_id'])
        if not user or user.role != 'admin':
            return create_response(
                success=False,
                error='Admin privileges required'
            ), 403

        return f(*args, **kwargs)
    return decorated_function


def create_response(success: bool = True, data=None, message: str = '', error: str = ''):
    """创建统一的API响应格式"""
    response = {
        'success': success,
        'timestamp': datetime.now().isoformat(),
    }
    
    if data is not None:
        response['data'] = data
    if message:
        response['message'] = message
    if error:
        response['error'] = error
        
    return jsonify(response)


# 认证相关API
@api_bp.route('/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        # 简单的认证逻辑（实际应用中应使用更安全的方式）
        if username == 'admin' and password == 'admin':
            user_data = {
                'id': '1',
                'username': username,
                'role': 'admin',
                'isAuthenticated': True,
            }

            # 设置会话
            session['user_id'] = '1'
            session['username'] = username
            session['role'] = 'admin'

            return create_response(
                success=True,
                data={'user': user_data, 'token': session.get('_id', 'session_token')},
                message='登录成功'
            )
        else:
            return create_response(
                success=False,
                error='用户名或密码错误'
            ), 401
            
    except Exception as e:
        logger.error(f"Login error: {e}")
        return create_response(
            success=False,
            error='登录失败'
        ), 500


@api_bp.route('/auth/logout', methods=['POST'])
@login_required
def logout():
    """用户登出"""
    session.clear()
    return create_response(
        success=True,
        message='登出成功'
    )


@api_bp.route('/auth/status', methods=['GET'])
def auth_status():
    """检查认证状态"""
    if 'user_id' in session:
        user_data = {
            'id': session['user_id'],
            'username': session['username'],
            'role': session.get('role', 'user'),
            'isAuthenticated': True,
        }
        return create_response(
            success=True,
            data={
                'authenticated': True,
                'user': user_data
            }
        )

    return create_response(
        success=True,
        data={'authenticated': False}
    )


# 账号管理API
@api_bp.route('/accounts', methods=['GET'])
@login_required
def get_accounts():
    """获取账号列表"""
    try:
        return create_response(data=accounts_storage)
    except Exception as e:
        logger.error(f"Get accounts error: {e}")
        return create_response(success=False, error='获取账号列表失败'), 500


@api_bp.route('/accounts', methods=['POST'])
@login_required
def create_account():
    """创建新账号"""
    try:
        data = request.get_json()
        new_account = {
            'id': str(uuid.uuid4()),
            'name': data.get('name'),
            'phone': data.get('phone'),
            'isActive': False,
            'lastLogin': None,
        }
        accounts_storage.append(new_account)
        
        return create_response(
            data=new_account,
            message='账号创建成功'
        ), 201
        
    except Exception as e:
        logger.error(f"Create account error: {e}")
        return create_response(success=False, error='创建账号失败'), 500


@api_bp.route('/accounts/<account_id>', methods=['DELETE'])
@login_required
def delete_account(account_id: str):
    """删除账号"""
    try:
        global accounts_storage
        accounts_storage = [acc for acc in accounts_storage if acc['id'] != account_id]
        
        return create_response(message='账号删除成功')
        
    except Exception as e:
        logger.error(f"Delete account error: {e}")
        return create_response(success=False, error='删除账号失败'), 500


# 群组管理API
@api_bp.route('/groups', methods=['GET'])
@login_required
def get_groups():
    """获取群组列表"""
    try:
        account_id = request.args.get('accountId')
        # 实际应用中应根据accountId过滤群组
        return create_response(data=groups_storage)
    except Exception as e:
        logger.error(f"Get groups error: {e}")
        return create_response(success=False, error='获取群组列表失败'), 500


# 任务管理API
@api_bp.route('/tasks', methods=['GET'])
@login_required
def get_tasks():
    """获取任务列表"""
    try:
        task_type = request.args.get('type')
        status = request.args.get('status')
        
        tasks = list(tasks_storage.values())
        
        if task_type:
            tasks = [task for task in tasks if task.get('type') == task_type]
        if status:
            tasks = [task for task in tasks if task.get('status') == status]
            
        return create_response(data=tasks)
        
    except Exception as e:
        logger.error(f"Get tasks error: {e}")
        return create_response(success=False, error='获取任务列表失败'), 500


@api_bp.route('/tasks', methods=['POST'])
@login_required
def create_task():
    """创建新任务"""
    try:
        data = request.get_json()
        task_id = str(uuid.uuid4())
        
        new_task = {
            'id': task_id,
            'type': data.get('type'),
            'status': 'pending',
            'accountId': data.get('accountId'),
            'sourceGroupId': data.get('sourceGroupId'),
            'targetGroupId': data.get('targetGroupId'),
            'startMessageId': data.get('startMessageId'),
            'endMessageId': data.get('endMessageId'),
            'progress': 0,
            'totalFiles': 0,
            'completedFiles': 0,
            'failedFiles': 0,
            'downloadSpeed': None,
            'createdAt': datetime.now().isoformat(),
            'updatedAt': datetime.now().isoformat(),
            'error': None,
        }
        
        # 添加特定类型的字段
        if data.get('type') == 'download':
            new_task.update({
                'downloadPath': data.get('downloadPath', './downloads'),
                'fileTypes': data.get('fileTypes', []),
                'filter': data.get('filter'),
            })
        elif data.get('type') in ['forward', 'listen_forward']:
            new_task.update({
                'forwardRules': data.get('forwardRules', []),
            })
            if data.get('type') == 'listen_forward':
                new_task['isListening'] = False
        
        tasks_storage[task_id] = new_task
        
        return create_response(
            data=new_task,
            message='任务创建成功'
        ), 201
        
    except Exception as e:
        logger.error(f"Create task error: {e}")
        return create_response(success=False, error='创建任务失败'), 500


@api_bp.route('/tasks/<task_id>', methods=['PUT'])
@login_required
def update_task(task_id: str):
    """更新任务"""
    try:
        if task_id not in tasks_storage:
            return create_response(success=False, error='任务不存在'), 404
            
        data = request.get_json()
        task = tasks_storage[task_id]
        
        # 更新任务字段
        for key, value in data.items():
            if key != 'id':  # 不允许修改ID
                task[key] = value
                
        task['updatedAt'] = datetime.now().isoformat()
        
        return create_response(data=task, message='任务更新成功')
        
    except Exception as e:
        logger.error(f"Update task error: {e}")
        return create_response(success=False, error='更新任务失败'), 500


@api_bp.route('/tasks/<task_id>', methods=['DELETE'])
@login_required
def delete_task(task_id: str):
    """删除任务"""
    try:
        if task_id not in tasks_storage:
            return create_response(success=False, error='任务不存在'), 404
            
        del tasks_storage[task_id]
        
        return create_response(message='任务删除成功')
        
    except Exception as e:
        logger.error(f"Delete task error: {e}")
        return create_response(success=False, error='删除任务失败'), 500


# 系统设置API
@api_bp.route('/settings', methods=['GET'])
@login_required
def get_settings():
    """获取系统设置"""
    try:
        return create_response(data=settings_storage)
    except Exception as e:
        logger.error(f"Get settings error: {e}")
        return create_response(success=False, error='获取设置失败'), 500


@api_bp.route('/settings', methods=['PUT'])
@login_required
def update_settings():
    """更新系统设置"""
    try:
        data = request.get_json()
        settings_storage.update(data)
        
        return create_response(data=settings_storage, message='设置更新成功')
        
    except Exception as e:
        logger.error(f"Update settings error: {e}")
        return create_response(success=False, error='更新设置失败'), 500


# 统计信息API
@api_bp.route('/stats/overview', methods=['GET'])
@login_required
def get_stats_overview():
    """获取统计概览"""
    try:
        tasks = list(tasks_storage.values())
        
        stats = {
            'totalTasks': len(tasks),
            'runningTasks': len([t for t in tasks if t['status'] == 'running']),
            'completedTasks': len([t for t in tasks if t['status'] == 'completed']),
            'failedTasks': len([t for t in tasks if t['status'] == 'failed']),
            'downloadSpeed': format_byte(get_total_download_speed()) + '/s',
            'totalDownloaded': sum(t.get('completedFiles', 0) for t in tasks),
        }
        
        return create_response(data=stats)
        
    except Exception as e:
        logger.error(f"Get stats overview error: {e}")
        return create_response(success=False, error='获取统计信息失败'), 500


def register_api_routes(app):
    """注册API路由到Flask应用"""
    app.register_blueprint(api_bp)
