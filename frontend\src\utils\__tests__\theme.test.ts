import { describe, it, expect, beforeEach, vi } from 'vitest'
import { getTheme, getSystemTheme, presetThemes } from '../theme'

describe('theme utils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getTheme', () => {
    it('should return light theme by default', () => {
      const theme = getTheme('light')
      expect(theme).toBeDefined()
      expect(theme.token?.colorPrimary).toBeDefined()
    })

    it('should return dark theme when specified', () => {
      const theme = getTheme('dark')
      expect(theme).toBeDefined()
      expect(theme.algorithm).toBeDefined()
    })

    it('should return theme with custom color scheme', () => {
      const theme = getTheme('light', 'green')
      expect(theme).toBeDefined()
      expect(theme.token?.colorPrimary).toBe(presetThemes.green.primary)
    })

    it('should fallback to blue theme for invalid color scheme', () => {
      const theme = getTheme('light', 'invalid')
      expect(theme).toBeDefined()
      expect(theme.token?.colorPrimary).toBe(presetThemes.blue.primary)
    })
  })

  describe('getSystemTheme', () => {
    it('should return light theme when matchMedia is not available', () => {
      // Mock window.matchMedia to be undefined
      const originalMatchMedia = window.matchMedia
      delete (window as any).matchMedia
      
      const systemTheme = getSystemTheme()
      expect(systemTheme).toBe('light')
      
      // Restore
      window.matchMedia = originalMatchMedia
    })

    it('should return dark theme when system prefers dark', () => {
      // Mock window.matchMedia to return dark preference
      window.matchMedia = vi.fn().mockImplementation(query => ({
        matches: query === '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }))
      
      const systemTheme = getSystemTheme()
      expect(systemTheme).toBe('dark')
    })

    it('should return light theme when system prefers light', () => {
      // Mock window.matchMedia to return light preference
      window.matchMedia = vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }))
      
      const systemTheme = getSystemTheme()
      expect(systemTheme).toBe('light')
    })
  })

  describe('presetThemes', () => {
    it('should contain all expected theme presets', () => {
      const expectedThemes = ['blue', 'green', 'purple', 'gold', 'cyan']
      
      expectedThemes.forEach(themeName => {
        expect(presetThemes[themeName as keyof typeof presetThemes]).toBeDefined()
        expect(presetThemes[themeName as keyof typeof presetThemes].name).toBeDefined()
        expect(presetThemes[themeName as keyof typeof presetThemes].primary).toBeDefined()
        expect(presetThemes[themeName as keyof typeof presetThemes].light).toBeDefined()
        expect(presetThemes[themeName as keyof typeof presetThemes].dark).toBeDefined()
      })
    })

    it('should have consistent structure for all themes', () => {
      Object.values(presetThemes).forEach(theme => {
        expect(theme).toHaveProperty('name')
        expect(theme).toHaveProperty('primary')
        expect(theme).toHaveProperty('light')
        expect(theme).toHaveProperty('dark')
        expect(typeof theme.name).toBe('string')
        expect(typeof theme.primary).toBe('string')
        expect(typeof theme.light).toBe('object')
        expect(typeof theme.dark).toBe('object')
      })
    })
  })
})
