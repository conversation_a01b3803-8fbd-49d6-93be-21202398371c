"""
FastAPI WebSocket模块
"""

import json
import logging
from datetime import datetime
from typing import Dict, Set
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from fastapi.websockets import WebSocketState

from .auth import verify_token
from .config import get_settings

logger = logging.getLogger(__name__)

# WebSocket路由器
websocket_router = APIRouter()

# 连接管理器
class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_connections: Dict[str, Set[str]] = {}
    
    async def connect(self, websocket: WebSocket, connection_id: str, user_id: str = None):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = set()
            self.user_connections[user_id].add(connection_id)
        
        logger.info(f"WebSocket连接建立: {connection_id}, 用户: {user_id}")
    
    def disconnect(self, connection_id: str, user_id: str = None):
        """断开WebSocket连接"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        
        if user_id and user_id in self.user_connections:
            self.user_connections[user_id].discard(connection_id)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        logger.info(f"WebSocket连接断开: {connection_id}, 用户: {user_id}")
    
    async def send_personal_message(self, message: dict, connection_id: str):
        """发送个人消息"""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            if websocket.client_state == WebSocketState.CONNECTED:
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"发送消息失败: {e}")
                    self.disconnect(connection_id)
    
    async def send_user_message(self, message: dict, user_id: str):
        """发送用户消息（所有连接）"""
        if user_id in self.user_connections:
            for connection_id in self.user_connections[user_id].copy():
                await self.send_personal_message(message, connection_id)
    
    async def broadcast(self, message: dict):
        """广播消息"""
        for connection_id in list(self.active_connections.keys()):
            await self.send_personal_message(message, connection_id)
    
    def get_connection_count(self) -> int:
        """获取连接数"""
        return len(self.active_connections)
    
    def get_user_count(self) -> int:
        """获取用户数"""
        return len(self.user_connections)


# 全局连接管理器
manager = ConnectionManager()


def create_message(event: str, data: dict = None, message: str = None) -> dict:
    """创建WebSocket消息"""
    return {
        "event": event,
        "data": data or {},
        "message": message,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }


@websocket_router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点（无认证）"""
    connection_id = f"conn_{datetime.utcnow().timestamp()}"
    
    await manager.connect(websocket, connection_id)
    
    try:
        # 发送连接成功消息
        await manager.send_personal_message(
            create_message("connected", {"connection_id": connection_id}, "连接成功"),
            connection_id
        )
        
        while True:
            # 接收消息
            data = await websocket.receive_text()
            try:
                message = json.loads(data)
                event = message.get("event")
                
                # 处理不同类型的消息
                if event == "ping":
                    await manager.send_personal_message(
                        create_message("pong", {"timestamp": datetime.utcnow().isoformat()}),
                        connection_id
                    )
                elif event == "echo":
                    await manager.send_personal_message(
                        create_message("echo", message.get("data", {})),
                        connection_id
                    )
                else:
                    await manager.send_personal_message(
                        create_message("error", {"error": f"未知事件类型: {event}"}),
                        connection_id
                    )
                    
            except json.JSONDecodeError:
                await manager.send_personal_message(
                    create_message("error", {"error": "无效的JSON格式"}),
                    connection_id
                )
            except Exception as e:
                logger.error(f"处理WebSocket消息失败: {e}")
                await manager.send_personal_message(
                    create_message("error", {"error": "消息处理失败"}),
                    connection_id
                )
                
    except WebSocketDisconnect:
        manager.disconnect(connection_id)
    except Exception as e:
        logger.error(f"WebSocket连接异常: {e}")
        manager.disconnect(connection_id)


@websocket_router.websocket("/ws/auth")
async def authenticated_websocket_endpoint(websocket: WebSocket):
    """需要认证的WebSocket端点"""
    connection_id = f"auth_conn_{datetime.utcnow().timestamp()}"
    user_id = None
    
    try:
        # 等待认证消息
        await websocket.accept()
        auth_data = await websocket.receive_text()
        
        try:
            auth_message = json.loads(auth_data)
            token = auth_message.get("token")
            
            if not token:
                await websocket.send_text(json.dumps(
                    create_message("auth_error", {"error": "缺少认证令牌"})
                ))
                await websocket.close()
                return
            
            # 验证令牌
            token_data = verify_token(token)
            if not token_data:
                await websocket.send_text(json.dumps(
                    create_message("auth_error", {"error": "无效的认证令牌"})
                ))
                await websocket.close()
                return
            
            user_id = token_data.user_id
            
        except json.JSONDecodeError:
            await websocket.send_text(json.dumps(
                create_message("auth_error", {"error": "无效的认证消息格式"})
            ))
            await websocket.close()
            return
        
        # 认证成功，建立连接
        await manager.connect(websocket, connection_id, user_id)
        
        # 发送认证成功消息
        await manager.send_personal_message(
            create_message("auth_success", {
                "connection_id": connection_id,
                "user_id": user_id
            }, "认证成功"),
            connection_id
        )
        
        while True:
            # 接收消息
            data = await websocket.receive_text()
            try:
                message = json.loads(data)
                event = message.get("event")
                
                # 处理认证用户的消息
                if event == "ping":
                    await manager.send_personal_message(
                        create_message("pong", {"timestamp": datetime.utcnow().isoformat()}),
                        connection_id
                    )
                elif event == "get_status":
                    await manager.send_personal_message(
                        create_message("status", {
                            "connections": manager.get_connection_count(),
                            "users": manager.get_user_count()
                        }),
                        connection_id
                    )
                else:
                    await manager.send_personal_message(
                        create_message("error", {"error": f"未知事件类型: {event}"}),
                        connection_id
                    )
                    
            except json.JSONDecodeError:
                await manager.send_personal_message(
                    create_message("error", {"error": "无效的JSON格式"}),
                    connection_id
                )
            except Exception as e:
                logger.error(f"处理认证WebSocket消息失败: {e}")
                await manager.send_personal_message(
                    create_message("error", {"error": "消息处理失败"}),
                    connection_id
                )
                
    except WebSocketDisconnect:
        manager.disconnect(connection_id, user_id)
    except Exception as e:
        logger.error(f"认证WebSocket连接异常: {e}")
        manager.disconnect(connection_id, user_id)


# 导出连接管理器供其他模块使用
def get_connection_manager() -> ConnectionManager:
    """获取连接管理器"""
    return manager
