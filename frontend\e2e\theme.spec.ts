import { test, expect } from '@playwright/test';

test.describe('Theme Management', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/login');
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'admin');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('should toggle theme using header button', async ({ page }) => {
    // 点击用户头像打开下拉菜单
    await page.click('.ant-avatar');
    
    // 点击主题切换按钮
    await page.click('text=切换到暗色模式');
    
    // 验证主题已切换（可以通过检查body的类名或样式）
    await expect(page.locator('body')).toHaveAttribute('data-theme', 'dark');
    
    // 再次切换回亮色模式
    await page.click('.ant-avatar');
    await page.click('text=切换到亮色模式');
    
    await expect(page.locator('body')).not.toHaveAttribute('data-theme', 'dark');
  });

  test('should open theme settings modal', async ({ page }) => {
    // 点击用户头像打开下拉菜单
    await page.click('.ant-avatar');
    
    // 点击主题设置
    await page.click('text=主题设置');
    
    // 验证主题设置模态框打开
    await expect(page.locator('.ant-modal')).toBeVisible();
    await expect(page.locator('text=主题设置')).toBeVisible();
  });

  test('should change theme mode in settings', async ({ page }) => {
    // 打开主题设置
    await page.click('.ant-avatar');
    await page.click('text=主题设置');
    
    // 等待模态框打开
    await expect(page.locator('.ant-modal')).toBeVisible();
    
    // 选择暗色主题
    await page.click('text=暗色');
    
    // 验证主题已切换
    await page.waitForTimeout(500); // 等待主题切换动画
    
    // 选择亮色主题
    await page.click('text=亮色');
    
    // 验证主题已切换回来
    await page.waitForTimeout(500);
    
    // 选择自动模式
    await page.click('text=自动');
    
    // 验证自动模式已选中
    const autoButton = page.locator('input[value="auto"]');
    await expect(autoButton).toBeChecked();
  });

  test('should change color scheme', async ({ page }) => {
    // 打开主题设置
    await page.click('.ant-avatar');
    await page.click('text=主题设置');
    
    await expect(page.locator('.ant-modal')).toBeVisible();
    
    // 选择不同的颜色方案
    await page.click('text=自然绿');
    
    // 验证颜色方案已切换
    const greenOption = page.locator('input[value="green"]');
    await expect(greenOption).toBeChecked();
    
    // 选择优雅紫
    await page.click('text=优雅紫');
    
    const purpleOption = page.locator('input[value="purple"]');
    await expect(purpleOption).toBeChecked();
    
    // 切换回经典蓝
    await page.click('text=经典蓝');
    
    const blueOption = page.locator('input[value="blue"]');
    await expect(blueOption).toBeChecked();
  });

  test('should persist theme settings', async ({ page }) => {
    // 打开主题设置并切换到暗色模式
    await page.click('.ant-avatar');
    await page.click('text=主题设置');
    await page.click('text=暗色');
    
    // 关闭模态框
    await page.click('.ant-modal-close');
    
    // 刷新页面
    await page.reload();
    
    // 验证主题设置被保持
    await page.click('.ant-avatar');
    await page.click('text=主题设置');
    
    const darkOption = page.locator('input[value="dark"]');
    await expect(darkOption).toBeChecked();
  });

  test('should show theme preview', async ({ page }) => {
    // 打开主题设置
    await page.click('.ant-avatar');
    await page.click('text=主题设置');
    
    // 检查预览区域
    await expect(page.locator('text=主题预览')).toBeVisible();
    await expect(page.locator('text=这是一个主题预览区域')).toBeVisible();
    
    // 切换主题并检查预览变化
    await page.click('text=暗色');
    
    // 预览区域应该反映主题变化
    const previewArea = page.locator('text=这是一个主题预览区域').locator('..');
    await expect(previewArea).toBeVisible();
  });

  test('should handle theme transition animation', async ({ page }) => {
    // 启用主题切换动画
    await page.click('.ant-avatar');
    await page.click('text=主题设置');
    
    // 确保动画开关是开启的
    const transitionSwitch = page.locator('text=主题切换动画').locator('..').locator('.ant-switch');
    if (!(await transitionSwitch.locator('.ant-switch-checked').isVisible())) {
      await transitionSwitch.click();
    }
    
    // 切换主题
    await page.click('text=暗色');
    
    // 验证动画效果（这里只是简单检查主题确实切换了）
    await page.waitForTimeout(500);
    
    await page.click('text=亮色');
    await page.waitForTimeout(500);
  });

  test('should work with quick action buttons', async ({ page }) => {
    // 打开主题设置
    await page.click('.ant-avatar');
    await page.click('text=主题设置');
    
    // 使用快速操作按钮
    await page.click('button:has-text("切换到暗色")');
    
    // 验证主题已切换
    const darkOption = page.locator('input[value="dark"]');
    await expect(darkOption).toBeChecked();
    
    // 使用快速操作切换回亮色
    await page.click('button:has-text("切换到亮色")');
    
    const lightOption = page.locator('input[value="light"]');
    await expect(lightOption).toBeChecked();
    
    // 切换到跟随系统
    await page.click('button:has-text("跟随系统")');
    
    const autoOption = page.locator('input[value="auto"]');
    await expect(autoOption).toBeChecked();
  });
});
