import { theme } from 'antd';
import { blue, green, red, orange, purple, gold, cyan } from '@ant-design/colors';

export const lightTheme = {
  algorithm: theme.defaultAlgorithm,
  token: {
    colorPrimary: blue[5],
    colorSuccess: green[5],
    colorWarning: orange[5],
    colorError: red[5],
    colorInfo: blue[5],
    borderRadius: 6,
    wireframe: false,
    colorBgContainer: '#ffffff',
    colorBgElevated: '#ffffff',
    colorBgLayout: '#f5f5f5',
    colorBorder: '#d9d9d9',
    colorText: 'rgba(0, 0, 0, 0.88)',
    colorTextSecondary: 'rgba(0, 0, 0, 0.65)',
  },
  components: {
    Layout: {
      headerBg: '#ffffff',
      siderBg: '#f5f5f5',
      bodyBg: '#ffffff',
      triggerBg: '#ffffff',
    },
    Menu: {
      itemBg: 'transparent',
      itemSelectedBg: blue[1],
      itemHoverBg: blue[0],
      itemActiveBg: blue[2],
    },
    Card: {
      headerBg: '#fafafa',
      bodyBg: '#ffffff',
    },
    Button: {
      colorPrimary: blue[5],
      algorithm: true,
    },
  },
};

export const darkTheme = {
  algorithm: theme.darkAlgorithm,
  token: {
    colorPrimary: blue[4],
    colorSuccess: green[4],
    colorWarning: orange[4],
    colorError: red[4],
    colorInfo: blue[4],
    borderRadius: 6,
    wireframe: false,
    colorBgContainer: '#141414',
    colorBgElevated: '#1f1f1f',
    colorBgLayout: '#000000',
    colorBorder: '#424242',
    colorText: 'rgba(255, 255, 255, 0.88)',
    colorTextSecondary: 'rgba(255, 255, 255, 0.65)',
  },
  components: {
    Layout: {
      headerBg: '#001529',
      siderBg: '#001529',
      bodyBg: '#141414',
      triggerBg: '#001529',
    },
    Menu: {
      itemBg: 'transparent',
      itemSelectedBg: blue[9],
      itemHoverBg: blue[8],
      itemActiveBg: blue[7],
    },
    Card: {
      headerBg: '#1f1f1f',
      bodyBg: '#141414',
    },
    Button: {
      colorPrimary: blue[4],
      algorithm: true,
    },
  },
};

// 自定义主题色彩
export const customColors = {
  primary: blue[5],
  success: green[5],
  warning: orange[5],
  error: red[5],
  info: blue[5],
  purple: purple[5],
  gold: gold[5],
  cyan: cyan[5],
};

// 预设主题
export const presetThemes = {
  blue: {
    name: '经典蓝',
    primary: blue[5],
    light: { ...lightTheme, token: { ...lightTheme.token, colorPrimary: blue[5] } },
    dark: { ...darkTheme, token: { ...darkTheme.token, colorPrimary: blue[4] } },
  },
  green: {
    name: '自然绿',
    primary: green[5],
    light: { ...lightTheme, token: { ...lightTheme.token, colorPrimary: green[5] } },
    dark: { ...darkTheme, token: { ...darkTheme.token, colorPrimary: green[4] } },
  },
  purple: {
    name: '优雅紫',
    primary: purple[5],
    light: { ...lightTheme, token: { ...lightTheme.token, colorPrimary: purple[5] } },
    dark: { ...darkTheme, token: { ...darkTheme.token, colorPrimary: purple[4] } },
  },
  gold: {
    name: '温暖金',
    primary: gold[5],
    light: { ...lightTheme, token: { ...lightTheme.token, colorPrimary: gold[5] } },
    dark: { ...darkTheme, token: { ...darkTheme.token, colorPrimary: gold[4] } },
  },
  cyan: {
    name: '清新青',
    primary: cyan[5],
    light: { ...lightTheme, token: { ...lightTheme.token, colorPrimary: cyan[5] } },
    dark: { ...darkTheme, token: { ...darkTheme.token, colorPrimary: cyan[4] } },
  },
};

// 主题工具函数
export const getTheme = (mode: 'light' | 'dark', colorScheme: string = 'blue') => {
  const preset = presetThemes[colorScheme as keyof typeof presetThemes] || presetThemes.blue;
  return mode === 'dark' ? preset.dark : preset.light;
};

// 检测系统主题
export const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'light';
};

// 主题切换动画
export const applyThemeTransition = () => {
  // 添加过渡动画类
  document.body.classList.add('theme-transition');

  // 300ms后移除动画类
  setTimeout(() => {
    document.body.classList.remove('theme-transition');
  }, 300);
};
