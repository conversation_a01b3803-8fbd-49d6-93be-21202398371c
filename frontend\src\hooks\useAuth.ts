import { useEffect } from 'react';
import { useAuthStore } from '@/store/authStore';
import { STORAGE_KEYS } from '@/utils/constants';

export const useAuth = () => {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    clearError,
    setUser,
    setToken,
  } = useAuthStore();

  // 初始化时检查本地存储的token
  useEffect(() => {
    const storedToken = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    if (storedToken && !token) {
      setToken(storedToken);
      // TODO: 验证token有效性，获取用户信息
    }
  }, [token, setToken]);

  return {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    clearError,
    setUser,
    setToken,
  };
};
