# Telegram Media Downloader 开发者指南

## 项目概述

Telegram Media Downloader 是一个基于React + Flask的Web应用，用于下载和管理Telegram媒体文件。

### 技术栈

#### 前端
- **React 18**: 用户界面框架
- **TypeScript**: 类型安全的JavaScript
- **Vite**: 构建工具和开发服务器
- **Ant Design**: UI组件库
- **Zustand**: 状态管理
- **React Router**: 路由管理

#### 后端
- **Flask**: Python Web框架
- **SQLite/PostgreSQL**: 数据库
- **Redis**: 缓存和会话存储
- **WebSocket**: 实时通信
- **Pyrogram**: Telegram API客户端

#### 部署
- **Docker**: 容器化部署
- **Nginx**: 反向代理和静态文件服务
- **Prometheus**: 监控和指标收集
- **Grafana**: 数据可视化

## 开发环境搭建

### 前端开发

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行测试
npm run test

# 构建生产版本
npm run build
```

### 后端开发

```bash
# 安装Python依赖
pip install -r requirements.txt

# 启动开发服务器
python app.py

# 运行测试
python -m pytest

# 启动WebSocket服务
python websocket_server.py
```

## 项目结构

```
telegram-media-downloader/
├── frontend/                 # React前端
│   ├── src/
│   │   ├── components/      # 可复用组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── store/          # 状态管理
│   │   ├── utils/          # 工具函数
│   │   ├── types/          # TypeScript类型定义
│   │   └── test/           # 测试文件
│   ├── public/             # 静态资源
│   └── e2e/               # 端到端测试
├── module/                 # Python后端模块
│   ├── api.py             # RESTful API
│   ├── web.py             # Web服务器
│   ├── websocket_server.py # WebSocket服务
│   └── app.py             # 主应用
├── docs/                  # 文档
├── monitoring/            # 监控配置
└── docker-compose.yml     # Docker编排
```

## 核心概念

### 前端架构

#### 组件设计原则
1. **单一职责**: 每个组件只负责一个功能
2. **可复用性**: 组件应该易于在不同场景中复用
3. **可测试性**: 组件应该易于单元测试
4. **性能优化**: 使用React.memo、useMemo等优化性能

#### 状态管理
使用Zustand进行状态管理，按功能模块划分store：

```typescript
// 任务状态管理
interface TaskStore {
  tasks: Task[];
  isLoading: boolean;
  error: string | null;
  fetchTasks: () => Promise<void>;
  createTask: (task: Partial<Task>) => Promise<void>;
  updateTask: (id: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
}
```

#### 路由设计
使用React Router进行路由管理，支持：
- 嵌套路由
- 路由守卫
- 懒加载
- 动态路由

### 后端架构

#### API设计
遵循RESTful API设计原则：

```python
# 统一响应格式
{
  "success": true,
  "data": {...},
  "message": "操作成功",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### WebSocket通信
用于实时数据推送：

```python
# 事件类型
WS_EVENTS = {
    'TASK_PROGRESS': 'task_progress',
    'TASK_UPDATE': 'task_update',
    'SYSTEM_STATUS': 'system_status',
    'ERROR': 'error'
}
```

## 开发规范

### 代码风格

#### TypeScript/JavaScript
- 使用ESLint和Prettier进行代码格式化
- 遵循Airbnb JavaScript风格指南
- 使用TypeScript严格模式
- 组件使用函数式组件和Hooks

#### Python
- 遵循PEP 8代码风格
- 使用Black进行代码格式化
- 使用类型注解
- 编写文档字符串

### 命名规范

#### 文件命名
- 组件文件使用PascalCase: `TaskCard.tsx`
- 工具文件使用camelCase: `formatUtils.ts`
- 常量文件使用UPPER_CASE: `CONSTANTS.ts`

#### 变量命名
- 变量和函数使用camelCase
- 常量使用UPPER_CASE
- 组件使用PascalCase
- 接口使用PascalCase并以I开头（可选）

### Git工作流

#### 分支策略
- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成功能
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

#### 提交规范
使用Conventional Commits规范：

```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建工具或辅助工具的变动
```

## 测试策略

### 前端测试

#### 单元测试
使用Vitest和React Testing Library：

```typescript
import { render, screen } from '@/test/utils';
import TaskCard from '../TaskCard';

describe('TaskCard', () => {
  it('should render task information', () => {
    const task = mockTask();
    render(<TaskCard task={task} />);
    
    expect(screen.getByText(task.name)).toBeInTheDocument();
  });
});
```

#### 集成测试
使用Playwright进行端到端测试：

```typescript
test('should create download task', async ({ page }) => {
  await page.goto('/download');
  await page.fill('[placeholder="任务名称"]', 'Test Task');
  await page.click('button:has-text("创建任务")');
  
  await expect(page.locator('.success-message')).toBeVisible();
});
```

### 后端测试

#### 单元测试
使用pytest进行单元测试：

```python
def test_create_task():
    response = client.post('/api/tasks', json={
        'name': 'Test Task',
        'type': 'download'
    })
    
    assert response.status_code == 201
    assert response.json()['success'] is True
```

## 性能优化

### 前端优化

#### 代码分割
```typescript
// 路由级别的代码分割
const Dashboard = lazy(() => import('@/pages/Dashboard'));
const Download = lazy(() => import('@/pages/Download'));
```

#### 组件优化
```typescript
// 使用React.memo避免不必要的重渲染
const TaskCard = React.memo(({ task, onUpdate }) => {
  // 组件实现
});

// 使用useMemo缓存计算结果
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);
```

#### 虚拟滚动
对于大量数据的列表，使用虚拟滚动：

```typescript
import VirtualList from '@/components/VirtualList';

<VirtualList
  items={tasks}
  itemHeight={80}
  containerHeight={400}
  renderItem={(task) => <TaskCard task={task} />}
/>
```

### 后端优化

#### 数据库优化
- 添加适当的索引
- 使用连接池
- 实现查询缓存

#### 缓存策略
- Redis缓存热点数据
- 浏览器缓存静态资源
- API响应缓存

## 部署和运维

### 开发环境
```bash
# 启动所有服务
docker-compose -f docker-compose.dev.yml up

# 只启动后端服务
docker-compose up backend redis

# 前端开发服务器
cd frontend && npm run dev
```

### 生产环境
```bash
# 构建和部署
./deploy.sh prod up

# 查看服务状态
./deploy.sh prod health

# 查看日志
./deploy.sh prod logs
```

### 监控和日志

#### 应用监控
- 使用Prometheus收集指标
- 使用Grafana可视化数据
- 配置告警规则

#### 日志管理
- 结构化日志输出
- 日志级别控制
- 日志轮转和清理

## 常见问题

### 开发问题

#### Q: 如何调试WebSocket连接？
A: 
1. 检查浏览器开发者工具的Network标签
2. 查看WebSocket连接状态
3. 检查服务器端WebSocket日志

#### Q: 如何处理跨域问题？
A: 
1. 配置CORS允许的域名
2. 在开发环境使用代理
3. 确保Cookie设置正确

### 部署问题

#### Q: Docker容器启动失败？
A: 
1. 检查Docker日志
2. 验证环境变量配置
3. 确保端口没有冲突

#### Q: 如何更新生产环境？
A: 
1. 备份当前数据
2. 拉取最新代码
3. 重新构建镜像
4. 滚动更新服务

## 贡献指南

### 提交代码
1. Fork项目到个人仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request

### 代码审查
- 确保代码符合规范
- 添加必要的测试
- 更新相关文档
- 通过CI/CD检查

### 发布流程
1. 更新版本号
2. 生成变更日志
3. 创建Release标签
4. 部署到生产环境

## 资源链接

- [React官方文档](https://react.dev/)
- [Ant Design文档](https://ant.design/)
- [Flask官方文档](https://flask.palletsprojects.com/)
- [Docker文档](https://docs.docker.com/)
