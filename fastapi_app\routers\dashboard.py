"""
仪表板API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from ..auth import get_current_active_user, User
from ..database import get_db, Task, TelegramAccount, TelegramGroup
from ..utils import create_response

router = APIRouter()


class TaskStats(BaseModel):
    """任务统计模型"""
    total: int
    running: int
    completed: int
    failed: int
    pending: int


class DownloadStats(BaseModel):
    """下载统计模型"""
    totalFiles: int
    totalSize: str
    todayFiles: int
    todaySize: str


class SystemStats(BaseModel):
    """系统统计模型"""
    uptime: str
    memoryUsage: str
    diskUsage: str
    activeConnections: int


class DashboardResponse(BaseModel):
    """仪表板响应模型"""
    taskStats: TaskStats
    downloadStats: DownloadStats
    systemStats: SystemStats
    recentTasks: list
    chartData: dict


@router.get("")
async def get_dashboard(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取仪表板数据"""
    try:
        # 获取任务统计
        task_stats_query = select(
            func.count(Task.id).label('total'),
            func.sum(func.case((Task.status == 'running', 1), else_=0)).label('running'),
            func.sum(func.case((Task.status == 'completed', 1), else_=0)).label('completed'),
            func.sum(func.case((Task.status == 'failed', 1), else_=0)).label('failed'),
            func.sum(func.case((Task.status == 'pending', 1), else_=0)).label('pending')
        )
        
        task_stats_result = await db.execute(task_stats_query)
        task_stats_row = task_stats_result.first()
        
        task_stats = TaskStats(
            total=task_stats_row.total or 0,
            running=task_stats_row.running or 0,
            completed=task_stats_row.completed or 0,
            failed=task_stats_row.failed or 0,
            pending=task_stats_row.pending or 0
        )
        
        # 获取最近任务
        recent_tasks_query = select(Task).order_by(Task.created_at.desc()).limit(5)
        recent_tasks_result = await db.execute(recent_tasks_query)
        recent_tasks = recent_tasks_result.scalars().all()
        
        recent_tasks_list = []
        for task in recent_tasks:
            recent_tasks_list.append({
                "id": task.task_id,
                "name": task.name,
                "status": task.status,
                "progress": task.progress,
                "createdAt": task.created_at.isoformat() + "Z"
            })
        
        # 模拟其他统计数据（实际应用中应从真实数据源获取）
        download_stats = DownloadStats(
            totalFiles=0,
            totalSize="0 B",
            todayFiles=0,
            todaySize="0 B"
        )
        
        system_stats = SystemStats(
            uptime="0 minutes",
            memoryUsage="0 MB",
            diskUsage="0%",
            activeConnections=0
        )
        
        chart_data = {
            "dailyDownloads": [],
            "taskStatusDistribution": [
                {"name": "完成", "value": task_stats.completed},
                {"name": "运行中", "value": task_stats.running},
                {"name": "失败", "value": task_stats.failed},
                {"name": "等待中", "value": task_stats.pending}
            ],
            "fileTypeDistribution": []
        }
        
        dashboard_data = DashboardResponse(
            taskStats=task_stats,
            downloadStats=download_stats,
            systemStats=system_stats,
            recentTasks=recent_tasks_list,
            chartData=chart_data
        )
        
        return create_response(
            success=True,
            data=dashboard_data.dict()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取仪表板数据失败: {str(e)}"
        )
