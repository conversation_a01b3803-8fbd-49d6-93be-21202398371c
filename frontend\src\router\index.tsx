import React from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import MainLayout from '@/components/Layout/MainLayout';
import AuthLayout from '@/components/Layout/AuthLayout';

// 页面组件 - 使用懒加载优化
const Dashboard = React.lazy(() =>
  import('@/pages/Dashboard').then(module => ({ default: module.default }))
);
const Download = React.lazy(() =>
  import('@/pages/Download').then(module => ({ default: module.default }))
);
const Forward = React.lazy(() =>
  import('@/pages/Forward').then(module => ({ default: module.default }))
);
const Listen = React.lazy(() =>
  import('@/pages/Listen').then(module => ({ default: module.default }))
);
const Settings = React.lazy(() =>
  import('@/pages/Settings').then(module => ({ default: module.default }))
);
const Login = React.lazy(() =>
  import('@/pages/Login').then(module => ({ default: module.default }))
);

// 路由保护组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // TODO: 实现真实的认证检查
  const isAuthenticated = localStorage.getItem('auth_token');
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

// 主题切换 Hook
const useTheme = () => {
  const [themeMode, setThemeMode] = React.useState<'light' | 'dark' | 'auto'>(() => {
    const saved = localStorage.getItem('theme-mode');
    return (saved as 'light' | 'dark' | 'auto') || 'auto';
  });

  const getCurrentActualMode = () => {
    if (themeMode === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return themeMode;
  };

  const isDarkMode = getCurrentActualMode() === 'dark';

  const toggleTheme = () => {
    const currentActual = getCurrentActualMode();
    const newMode = currentActual === 'dark' ? 'light' : 'dark';
    setThemeMode(newMode);
    localStorage.setItem('theme-mode', newMode);
    localStorage.setItem('theme', newMode); // 兼容旧版本

    // 触发存储事件以通知其他组件
    window.dispatchEvent(new StorageEvent('storage', {
      key: 'theme-mode',
      newValue: newMode,
      oldValue: currentActual
    }));
  };

  const openThemeSettings = () => {
    if ((window as any).openThemeSettings) {
      (window as any).openThemeSettings();
    } else {
      console.error('window.openThemeSettings 不存在');
    }
  };

  // 监听系统主题变化
  React.useEffect(() => {
    if (themeMode === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => {
        // 触发重新渲染
        setThemeMode('auto');
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [themeMode]);

  // 监听localStorage变化
  React.useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'theme-mode') {
        setThemeMode((e.newValue as 'light' | 'dark' | 'auto') || 'auto');
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  return { isDarkMode, toggleTheme, openThemeSettings };
};

// 主布局包装器
const MainLayoutWrapper: React.FC = () => {
  const { isDarkMode, toggleTheme, openThemeSettings } = useTheme();

  return (
    <ProtectedRoute>
      <MainLayout
        onThemeToggle={toggleTheme}
        isDarkMode={isDarkMode}
        onOpenThemeSettings={openThemeSettings}
      />
    </ProtectedRoute>
  );
};

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/dashboard" replace />,
  },
  {
    path: '/login',
    element: <AuthLayout />,
    children: [
      {
        index: true,
        element: (
          <React.Suspense fallback={
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100vh'
            }}>
              Loading...
            </div>
          }>
            <Login />
          </React.Suspense>
        ),
      },
    ],
  },
  {
    path: '/',
    element: <MainLayoutWrapper />,
    children: [
      {
        path: 'dashboard',
        element: (
          <React.Suspense fallback={<div style={{ padding: 24, textAlign: 'center' }}>Loading Dashboard...</div>}>
            <Dashboard />
          </React.Suspense>
        ),
      },
      {
        path: 'download',
        element: (
          <React.Suspense fallback={<div>Loading...</div>}>
            <Download />
          </React.Suspense>
        ),
      },
      {
        path: 'forward',
        element: (
          <React.Suspense fallback={<div>Loading...</div>}>
            <Forward />
          </React.Suspense>
        ),
      },
      {
        path: 'listen',
        element: (
          <React.Suspense fallback={<div>Loading...</div>}>
            <Listen />
          </React.Suspense>
        ),
      },
      {
        path: 'settings',
        element: (
          <React.Suspense fallback={<div>Loading...</div>}>
            <Settings />
          </React.Suspense>
        ),
      },
    ],
  },
  {
    path: '*',
    element: <Navigate to="/dashboard" replace />,
  },
]);

export default router;
