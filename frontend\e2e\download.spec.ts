import { test, expect } from '@playwright/test';

test.describe('Download Management', () => {
  test.beforeEach(async ({ page }) => {
    // 登录并导航到下载页面
    await page.goto('/login');
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'admin');
    await page.click('button[type="submit"]');
    await page.goto('/download');
  });

  test('should display download page correctly', async ({ page }) => {
    await expect(page.locator('h2')).toContainText('下载管理');
    await expect(page.locator('text=创建下载任务')).toBeVisible();
    await expect(page.locator('text=下载任务列表')).toBeVisible();
  });

  test('should create download task successfully', async ({ page }) => {
    // 选择账号
    await page.click('.ant-select-selector', { nth: 0 });
    await page.click('text=主账号');

    // 选择群组
    await page.click('.ant-select-selector', { nth: 1 });
    await page.click('text=技术交流群');

    // 填写起始消息ID
    await page.fill('input[placeholder="1"]', '1');

    // 填写结束消息ID
    await page.fill('input[placeholder="最新"]', '100');

    // 选择文件类型
    await page.check('input[value="image"]');
    await page.check('input[value="video"]');

    // 点击创建任务按钮
    await page.click('button:has-text("创建下载任务")');

    // 验证成功消息
    await expect(page.locator('.ant-message')).toContainText('下载任务创建成功');
  });

  test('should validate required fields', async ({ page }) => {
    // 直接点击创建按钮，不填写任何字段
    await page.click('button:has-text("创建下载任务")');

    // 应该显示验证错误
    await expect(page.locator('.ant-form-item-explain-error')).toBeVisible();
  });

  test('should display task list', async ({ page }) => {
    // 检查任务列表区域
    const taskList = page.locator('text=下载任务列表').locator('..');
    await expect(taskList).toBeVisible();

    // 如果有任务，应该显示任务卡片
    const taskCards = page.locator('.ant-list-item');
    if (await taskCards.count() > 0) {
      await expect(taskCards.first()).toBeVisible();
    }
  });

  test('should handle task actions', async ({ page }) => {
    // 首先创建一个任务
    await page.click('.ant-select-selector', { nth: 0 });
    await page.click('text=主账号');
    await page.click('.ant-select-selector', { nth: 1 });
    await page.click('text=技术交流群');
    await page.fill('input[placeholder="1"]', '1');
    await page.click('button:has-text("创建下载任务")');

    // 等待任务创建成功
    await expect(page.locator('.ant-message')).toContainText('下载任务创建成功');

    // 查找任务卡片中的更多按钮
    const moreButton = page.locator('.ant-btn').filter({ hasText: /more/i }).first();
    if (await moreButton.isVisible()) {
      await moreButton.click();

      // 检查下拉菜单选项
      await expect(page.locator('text=开始')).toBeVisible();
      await expect(page.locator('text=删除')).toBeVisible();
    }
  });

  test('should be responsive on mobile', async ({ page }) => {
    // 设置移动设备视口
    await page.setViewportSize({ width: 375, height: 667 });

    // 检查页面在移动设备上的显示
    await expect(page.locator('h2')).toContainText('下载管理');

    // 在移动设备上，表单和列表应该垂直堆叠
    const formCard = page.locator('text=创建下载任务').locator('..');
    const listCard = page.locator('text=下载任务列表').locator('..');

    await expect(formCard).toBeVisible();
    await expect(listCard).toBeVisible();
  });

  test('should handle file type selection', async ({ page }) => {
    // 测试文件类型复选框
    const imageCheckbox = page.locator('input[value="image"]');
    const videoCheckbox = page.locator('input[value="video"]');
    const audioCheckbox = page.locator('input[value="audio"]');
    const documentCheckbox = page.locator('input[value="document"]');

    // 选择多个文件类型
    await imageCheckbox.check();
    await videoCheckbox.check();

    await expect(imageCheckbox).toBeChecked();
    await expect(videoCheckbox).toBeChecked();
    await expect(audioCheckbox).not.toBeChecked();
    await expect(documentCheckbox).not.toBeChecked();

    // 取消选择
    await imageCheckbox.uncheck();
    await expect(imageCheckbox).not.toBeChecked();
  });

  test('should handle download path input', async ({ page }) => {
    const downloadPathInput = page.locator('input[placeholder="./downloads"]');
    
    // 清空并输入新路径
    await downloadPathInput.clear();
    await downloadPathInput.fill('/custom/download/path');

    await expect(downloadPathInput).toHaveValue('/custom/download/path');
  });

  test('should handle filter input', async ({ page }) => {
    const filterTextarea = page.locator('textarea[placeholder*="关键词"]');
    
    // 输入过滤条件
    await filterTextarea.fill('重要文件');

    await expect(filterTextarea).toHaveValue('重要文件');
  });
});
