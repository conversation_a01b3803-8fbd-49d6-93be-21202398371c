# Telegram Media Downloader 用户手册

## 目录
1. [快速开始](#快速开始)
2. [界面介绍](#界面介绍)
3. [功能详解](#功能详解)
4. [常见问题](#常见问题)
5. [技巧和建议](#技巧和建议)

## 快速开始

### 登录系统
1. 打开浏览器访问应用地址
2. 输入用户名和密码
3. 点击"登录"按钮

默认登录信息：
- 用户名: `admin`
- 密码: `admin`

### 添加Telegram账号
1. 进入"系统设置" → "账号管理"
2. 点击"添加账号"按钮
3. 输入账号名称和手机号
4. 按照提示完成Telegram账号授权

## 界面介绍

### 主界面布局
- **顶部导航栏**: 显示应用标题、用户信息和主题切换
- **左侧菜单**: 功能模块导航
- **主内容区**: 当前页面的主要内容
- **反馈按钮**: 右下角的意见反馈浮动按钮

### 菜单功能
- **仪表板**: 系统概览和统计信息
- **下载管理**: 创建和管理下载任务
- **转发管理**: 配置消息转发任务
- **监听转发**: 实时监听和转发消息
- **系统设置**: 账号、下载、代理等设置

## 功能详解

### 1. 下载管理

#### 创建下载任务
1. 选择要使用的Telegram账号
2. 选择要下载的群组或频道
3. 设置消息范围（起始和结束消息ID）
4. 选择要下载的文件类型
5. 设置下载路径（可选）
6. 添加过滤条件（可选）
7. 点击"创建下载任务"

#### 管理下载任务
- **开始**: 启动暂停或等待中的任务
- **暂停**: 暂停正在运行的任务
- **停止**: 停止任务并清理临时文件
- **删除**: 删除任务记录

#### 下载进度监控
- 实时显示下载进度百分比
- 显示已完成/总文件数量
- 显示当前下载速度
- 显示任务状态和错误信息

### 2. 转发管理

#### 创建转发任务
1. 选择Telegram账号
2. 选择源群组（消息来源）
3. 选择目标群组（转发目标）
4. 设置消息范围
5. 配置转发规则（可选）
6. 点击"创建转发任务"

#### 转发规则配置
- **规则名称**: 便于识别的规则名称
- **源内容匹配**: 使用正则表达式匹配消息内容
- **目标内容替换**: 转发时的内容替换模板
- **启用/禁用**: 控制规则是否生效

### 3. 监听转发

#### 设置监听任务
1. 选择Telegram账号
2. 选择要监听的群组（可多选）
3. 选择转发目标群组（可多选）
4. 配置转发规则
5. 点击"创建监听任务"

#### 实时消息流
- 显示实时接收的消息
- 标记已转发的消息
- 支持消息类型过滤
- 自动滚动到最新消息

#### 监听控制
- **开始监听**: 开始接收实时消息
- **停止监听**: 停止消息监听
- **暂停转发**: 继续监听但暂停转发

### 4. 系统设置

#### 账号管理
- **添加账号**: 添加新的Telegram账号
- **编辑账号**: 修改账号信息
- **删除账号**: 移除不需要的账号
- **账号状态**: 查看账号在线状态

#### 下载设置
- **默认下载路径**: 设置文件下载位置
- **最大并发任务数**: 控制同时运行的任务数量
- **界面主题**: 选择亮色/暗色主题
- **界面语言**: 选择界面语言

#### 代理设置
- **启用代理**: 开启/关闭代理功能
- **代理类型**: HTTP或SOCKS5
- **代理服务器**: 代理服务器地址和端口
- **认证信息**: 代理用户名和密码（如需要）

#### 通知设置
- **启用通知**: 开启/关闭系统通知
- **任务完成通知**: 任务完成时通知
- **任务失败通知**: 任务失败时通知
- **系统警告通知**: 系统异常时通知

## 常见问题

### Q: 如何获取Telegram API凭据？
A: 
1. 访问 https://my.telegram.org/
2. 登录您的Telegram账号
3. 点击"API development tools"
4. 创建新应用获取API ID和API Hash

### Q: 为什么无法连接到Telegram？
A: 
1. 检查网络连接
2. 确认API凭据正确
3. 检查是否需要配置代理
4. 查看错误日志获取详细信息

### Q: 下载速度很慢怎么办？
A: 
1. 检查网络带宽
2. 减少并发任务数量
3. 配置合适的代理服务器
4. 避免在网络高峰期下载

### Q: 如何批量下载多个群组？
A: 
1. 为每个群组创建单独的下载任务
2. 设置合理的并发任务数量
3. 使用任务队列功能
4. 监控系统资源使用情况

### Q: 转发消息时如何避免被限制？
A: 
1. 控制转发频率
2. 使用多个账号轮换
3. 设置合理的延迟时间
4. 避免转发敏感内容

## 技巧和建议

### 下载优化
1. **合理设置并发数**: 根据网络和服务器性能调整
2. **使用过滤条件**: 只下载需要的文件类型
3. **分时段下载**: 避开网络高峰期
4. **定期清理**: 删除不需要的文件释放空间

### 转发策略
1. **测试规则**: 先在小范围测试转发规则
2. **监控状态**: 定期检查转发任务状态
3. **备份配置**: 保存重要的转发规则配置
4. **遵守规则**: 遵守Telegram使用条款

### 安全建议
1. **定期更改密码**: 定期更新登录密码
2. **限制访问**: 只允许信任的IP访问
3. **备份数据**: 定期备份重要数据
4. **监控日志**: 关注异常访问记录

### 性能优化
1. **清理缓存**: 定期清理浏览器缓存
2. **关闭无用标签**: 减少浏览器内存占用
3. **更新浏览器**: 使用最新版本浏览器
4. **检查网络**: 确保网络连接稳定

## 快捷键

- `Ctrl + /`: 打开快捷键帮助
- `Ctrl + Shift + T`: 切换主题
- `Ctrl + Shift + S`: 打开系统设置
- `Esc`: 关闭模态框
- `Tab`: 在表单字段间切换

## 支持和反馈

如果您在使用过程中遇到问题或有改进建议：

1. **应用内反馈**: 点击右下角反馈按钮
2. **查看日志**: 在开发者工具中查看控制台日志
3. **联系支持**: 通过邮件或GitHub Issues联系我们

感谢您使用Telegram Media Downloader！
