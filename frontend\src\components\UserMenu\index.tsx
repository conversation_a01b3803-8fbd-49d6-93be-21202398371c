import React, { useState, useRef, useEffect } from 'react';
import { Button, Avatar, Space, Typography, Menu, Card } from 'antd';
import {
  UserOutlined,
  BulbOutlined,
  SettingOutlined,
  LogoutOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';

const { Text } = Typography;

interface UserMenuProps {
  isMobile: boolean;
  isDarkMode: boolean;
  onThemeToggle: () => void;
  onOpenThemeSettings?: () => void;
  onLogout: () => void;
}

const UserMenu: React.FC<UserMenuProps> = ({
  isMobile,
  isDarkMode,
  onThemeToggle,
  onOpenThemeSettings,
  onLogout,
}) => {
  const [visible, setVisible] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const menuItems: MenuProps['items'] = [
    {
      key: 'theme',
      icon: <BulbOutlined />,
      label: isDarkMode ? '切换到亮色模式' : '切换到暗色模式',
    },
    {
      key: 'theme-settings',
      icon: <SettingOutlined />,
      label: '主题设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    setVisible(false);

    switch (key) {
      case 'theme':
        onThemeToggle();
        break;
      case 'theme-settings':
        if (onOpenThemeSettings) {
          onOpenThemeSettings();
        } else if ((window as any).openThemeSettings) {
          (window as any).openThemeSettings();
        } else {
          console.error('主题设置函数不存在');
        }
        break;
      case 'logout':
        onLogout();
        break;
      default:
        console.log('未处理的菜单项:', key);
    }
  };

  const handleButtonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('用户菜单按钮点击，当前状态:', visible);
    setVisible(!visible);
  };

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        buttonRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setVisible(false);
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [visible]);

  return (
    <div style={{ position: 'relative' }}>
      <Button
        ref={buttonRef}
        type="text"
        style={{
          height: 'auto',
          padding: '4px 8px',
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          border: 'none',
          background: 'transparent',
        }}
        onClick={handleButtonClick}
      >
        <Space>
          <Avatar size={isMobile ? 'small' : 'default'} icon={<UserOutlined />} />
          {!isMobile && <Text>管理员</Text>}
        </Space>
      </Button>

      {visible && (
        <div
          ref={menuRef}
          style={{
            position: 'absolute',
            top: '100%',
            right: 0,
            zIndex: 1000,
            marginTop: 4,
          }}
        >
          <Card
            size="small"
            style={{
              minWidth: 160,
              boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
              border: '1px solid var(--ant-color-border)',
            }}
            styles={{ body: { padding: 0 } }}
          >
            <Menu
              items={menuItems}
              onClick={handleMenuClick}
              style={{
                border: 'none',
                background: 'transparent',
              }}
            />
          </Card>
        </div>
      )}
    </div>
  );
};

export default UserMenu;
