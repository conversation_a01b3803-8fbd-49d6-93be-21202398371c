"""
配置管理模块
"""

import os
import yaml
from typing import Dict, Any, List
from dataclasses import dataclass, field

@dataclass
class TelegramConfig:
    """Telegram API配置"""
    api_id: int = 0
    api_hash: str = ""
    session_name: str = "telegram_session"

@dataclass
class DatabaseConfig:
    """数据库配置"""
    url: str = "sqlite:///app.db"
    echo: bool = False
    pool_size: int = 10
    max_overflow: int = 20

@dataclass
class RedisConfig:
    """Redis配置"""
    url: str = "redis://localhost:6379/0"
    password: str = ""
    decode_responses: bool = True

@dataclass
class ProxyConfig:
    """代理配置"""
    enabled: bool = False
    scheme: str = "http"
    hostname: str = ""
    port: int = 0
    username: str = ""
    password: str = ""

@dataclass
class NotificationConfig:
    """通知配置"""
    enabled: bool = True
    task_completion: bool = True
    task_failure: bool = True
    system_alerts: bool = True
    email_enabled: bool = False
    email_smtp_server: str = ""
    email_smtp_port: int = 587
    email_username: str = ""
    email_password: str = ""
    email_from: str = ""
    email_to: List[str] = field(default_factory=list)

@dataclass
class SecurityConfig:
    """安全配置"""
    secret_key: str = "your-secret-key-change-in-production"
    session_timeout: int = 3600  # 1小时
    max_login_attempts: int = 5
    login_attempt_timeout: int = 300  # 5分钟
    cors_origins: List[str] = field(default_factory=lambda: ["http://localhost:3000"])
    allowed_hosts: List[str] = field(default_factory=lambda: ["localhost", "127.0.0.1"])

@dataclass
class PerformanceConfig:
    """性能配置"""
    max_concurrent_tasks: int = 5
    max_download_speed: int = 0  # 0表示无限制，单位KB/s
    chunk_size: int = 1024 * 1024  # 1MB
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: int = 5

class Config:
    """主配置类"""
    
    def __init__(self, config_file: str = "config.yaml"):
        self.config_file = config_file
        self._config_data = {}
        self.load_config()
        
        # 初始化各个配置模块
        self.telegram = TelegramConfig()
        self.database = DatabaseConfig()
        self.redis = RedisConfig()
        self.proxy = ProxyConfig()
        self.notification = NotificationConfig()
        self.security = SecurityConfig()
        self.performance = PerformanceConfig()
        
        self._apply_config()
    
    def load_config(self):
        """加载配置文件"""
        # 首先从环境变量加载
        self._load_from_env()
        
        # 然后从配置文件加载（如果存在）
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f) or {}
                    self._config_data.update(file_config)
            except Exception as e:
                print(f"Warning: Failed to load config file {self.config_file}: {e}")
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        env_mappings = {
            # Telegram配置
            'TELEGRAM_API_ID': ('telegram', 'api_id', int),
            'TELEGRAM_API_HASH': ('telegram', 'api_hash', str),
            'TELEGRAM_SESSION_NAME': ('telegram', 'session_name', str),
            
            # 数据库配置
            'DATABASE_URL': ('database', 'url', str),
            'DATABASE_ECHO': ('database', 'echo', bool),
            
            # Redis配置
            'REDIS_URL': ('redis', 'url', str),
            'REDIS_PASSWORD': ('redis', 'password', str),
            
            # 代理配置
            'PROXY_ENABLED': ('proxy', 'enabled', bool),
            'PROXY_SCHEME': ('proxy', 'scheme', str),
            'PROXY_HOSTNAME': ('proxy', 'hostname', str),
            'PROXY_PORT': ('proxy', 'port', int),
            'PROXY_USERNAME': ('proxy', 'username', str),
            'PROXY_PASSWORD': ('proxy', 'password', str),
            
            # 安全配置
            'SECRET_KEY': ('security', 'secret_key', str),
            'SESSION_TIMEOUT': ('security', 'session_timeout', int),
            'CORS_ORIGINS': ('security', 'cors_origins', lambda x: x.split(',')),
            'ALLOWED_HOSTS': ('security', 'allowed_hosts', lambda x: x.split(',')),
            
            # 性能配置
            'MAX_CONCURRENT_TASKS': ('performance', 'max_concurrent_tasks', int),
            'MAX_DOWNLOAD_SPEED': ('performance', 'max_download_speed', int),
            'CHUNK_SIZE': ('performance', 'chunk_size', int),
            'TIMEOUT': ('performance', 'timeout', int),
            'RETRY_ATTEMPTS': ('performance', 'retry_attempts', int),
            'RETRY_DELAY': ('performance', 'retry_delay', int),
        }
        
        for env_key, (section, key, converter) in env_mappings.items():
            value = os.getenv(env_key)
            if value is not None:
                try:
                    if section not in self._config_data:
                        self._config_data[section] = {}
                    
                    if converter == bool:
                        self._config_data[section][key] = value.lower() in ('true', '1', 'yes', 'on')
                    elif converter == int:
                        self._config_data[section][key] = int(value)
                    elif callable(converter):
                        self._config_data[section][key] = converter(value)
                    else:
                        self._config_data[section][key] = value
                except (ValueError, TypeError) as e:
                    print(f"Warning: Invalid value for {env_key}: {value} ({e})")
    
    def _apply_config(self):
        """应用配置到各个模块"""
        # Telegram配置
        if 'telegram' in self._config_data:
            telegram_config = self._config_data['telegram']
            self.telegram.api_id = telegram_config.get('api_id', self.telegram.api_id)
            self.telegram.api_hash = telegram_config.get('api_hash', self.telegram.api_hash)
            self.telegram.session_name = telegram_config.get('session_name', self.telegram.session_name)
        
        # 数据库配置
        if 'database' in self._config_data:
            db_config = self._config_data['database']
            self.database.url = db_config.get('url', self.database.url)
            self.database.echo = db_config.get('echo', self.database.echo)
            self.database.pool_size = db_config.get('pool_size', self.database.pool_size)
            self.database.max_overflow = db_config.get('max_overflow', self.database.max_overflow)
        
        # Redis配置
        if 'redis' in self._config_data:
            redis_config = self._config_data['redis']
            self.redis.url = redis_config.get('url', self.redis.url)
            self.redis.password = redis_config.get('password', self.redis.password)
            self.redis.decode_responses = redis_config.get('decode_responses', self.redis.decode_responses)
        
        # 代理配置
        if 'proxy' in self._config_data:
            proxy_config = self._config_data['proxy']
            self.proxy.enabled = proxy_config.get('enabled', self.proxy.enabled)
            self.proxy.scheme = proxy_config.get('scheme', self.proxy.scheme)
            self.proxy.hostname = proxy_config.get('hostname', self.proxy.hostname)
            self.proxy.port = proxy_config.get('port', self.proxy.port)
            self.proxy.username = proxy_config.get('username', self.proxy.username)
            self.proxy.password = proxy_config.get('password', self.proxy.password)
        
        # 通知配置
        if 'notification' in self._config_data:
            notif_config = self._config_data['notification']
            self.notification.enabled = notif_config.get('enabled', self.notification.enabled)
            self.notification.task_completion = notif_config.get('task_completion', self.notification.task_completion)
            self.notification.task_failure = notif_config.get('task_failure', self.notification.task_failure)
            self.notification.system_alerts = notif_config.get('system_alerts', self.notification.system_alerts)
        
        # 安全配置
        if 'security' in self._config_data:
            security_config = self._config_data['security']
            self.security.secret_key = security_config.get('secret_key', self.security.secret_key)
            self.security.session_timeout = security_config.get('session_timeout', self.security.session_timeout)
            self.security.cors_origins = security_config.get('cors_origins', self.security.cors_origins)
            self.security.allowed_hosts = security_config.get('allowed_hosts', self.security.allowed_hosts)
        
        # 性能配置
        if 'performance' in self._config_data:
            perf_config = self._config_data['performance']
            self.performance.max_concurrent_tasks = perf_config.get('max_concurrent_tasks', self.performance.max_concurrent_tasks)
            self.performance.max_download_speed = perf_config.get('max_download_speed', self.performance.max_download_speed)
            self.performance.chunk_size = perf_config.get('chunk_size', self.performance.chunk_size)
            self.performance.timeout = perf_config.get('timeout', self.performance.timeout)
            self.performance.retry_attempts = perf_config.get('retry_attempts', self.performance.retry_attempts)
            self.performance.retry_delay = perf_config.get('retry_delay', self.performance.retry_delay)
    
    def get_flask_config(self) -> Dict[str, Any]:
        """获取Flask应用配置"""
        return {
            'SECRET_KEY': self.security.secret_key,
            'SQLALCHEMY_DATABASE_URI': self.database.url,
            'SQLALCHEMY_ECHO': self.database.echo,
            'SQLALCHEMY_TRACK_MODIFICATIONS': False,
            'SQLALCHEMY_ENGINE_OPTIONS': {
                'pool_size': self.database.pool_size,
                'max_overflow': self.database.max_overflow,
                'pool_pre_ping': True,
                'pool_recycle': 3600,
            },
            'CORS_ORIGINS': self.security.cors_origins,
            'SESSION_TIMEOUT': self.security.session_timeout,
            'MAX_CONTENT_LENGTH': 100 * 1024 * 1024,  # 100MB
            'JSON_AS_ASCII': False,
            'JSONIFY_PRETTYPRINT_REGULAR': True,
        }
    
    def get_telegram_config(self) -> Dict[str, Any]:
        """获取Telegram配置"""
        return {
            'api_id': self.telegram.api_id,
            'api_hash': self.telegram.api_hash,
            'session_name': self.telegram.session_name,
            'proxy': self.get_proxy_config() if self.proxy.enabled else None,
        }
    
    def get_proxy_config(self) -> Dict[str, Any]:
        """获取代理配置"""
        if not self.proxy.enabled:
            return None
        
        config = {
            'proxy_type': self.proxy.scheme,
            'addr': self.proxy.hostname,
            'port': self.proxy.port,
        }
        
        if self.proxy.username:
            config['username'] = self.proxy.username
        if self.proxy.password:
            config['password'] = self.proxy.password
        
        return config
    
    def save_config(self):
        """保存配置到文件"""
        config_data = {
            'telegram': {
                'api_id': self.telegram.api_id,
                'api_hash': self.telegram.api_hash,
                'session_name': self.telegram.session_name,
            },
            'database': {
                'url': self.database.url,
                'echo': self.database.echo,
            },
            'redis': {
                'url': self.redis.url,
                'password': self.redis.password,
            },
            'proxy': {
                'enabled': self.proxy.enabled,
                'scheme': self.proxy.scheme,
                'hostname': self.proxy.hostname,
                'port': self.proxy.port,
                'username': self.proxy.username,
                'password': self.proxy.password,
            },
            'notification': {
                'enabled': self.notification.enabled,
                'task_completion': self.notification.task_completion,
                'task_failure': self.notification.task_failure,
                'system_alerts': self.notification.system_alerts,
            },
            'security': {
                'secret_key': self.security.secret_key,
                'session_timeout': self.security.session_timeout,
                'cors_origins': self.security.cors_origins,
                'allowed_hosts': self.security.allowed_hosts,
            },
            'performance': {
                'max_concurrent_tasks': self.performance.max_concurrent_tasks,
                'max_download_speed': self.performance.max_download_speed,
                'chunk_size': self.performance.chunk_size,
                'timeout': self.performance.timeout,
                'retry_attempts': self.performance.retry_attempts,
                'retry_delay': self.performance.retry_delay,
            },
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            raise Exception(f"Failed to save config file: {e}")

# 全局配置实例
config = Config()
