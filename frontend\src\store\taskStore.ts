import { create } from 'zustand';
import type { Task, TaskType, TaskStatus } from '@/types';
import { apiClient } from '@/services/api';

interface TaskState {
  tasks: Task[];
  activeTasks: Task[];
  isLoading: boolean;
  error: string | null;
}

interface TaskActions {
  fetchTasks: () => Promise<void>;
  createTask: (task: Partial<Task>) => Promise<void>;
  updateTask: (id: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
  startTask: (id: string) => Promise<void>;
  pauseTask: (id: string) => Promise<void>;
  stopTask: (id: string) => Promise<void>;
  updateTaskProgress: (id: string, progress: number) => void;
  clearError: () => void;
  getTasksByType: (type: TaskType) => Task[];
  getTasksByStatus: (status: TaskStatus) => Task[];
}

type TaskStore = TaskState & TaskActions;

export const useTaskStore = create<TaskStore>((set, get) => ({
  // 初始状态
  tasks: [],
  activeTasks: [],
  isLoading: false,
  error: null,

  // 获取任务列表
  fetchTasks: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await apiClient.get('/api/tasks');
      console.log('Tasks response:', response);
      
      const tasks = response.data || response || [];
      
      set({
        tasks,
        activeTasks: tasks.filter((task: Task) => 
          task.status === 'running' || task.status === 'pending'
        ),
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '获取任务列表失败',
      });
    }
  },

  // 创建任务
  createTask: async (taskData: Partial<Task>) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await apiClient.post('/api/tasks', taskData);
      console.log('Create task response:', response);
      
      const newTask = response.data || response;
      
      set((state) => ({
        tasks: [...state.tasks, newTask],
        activeTasks: newTask.status === 'running' || newTask.status === 'pending'
          ? [...state.activeTasks, newTask]
          : state.activeTasks,
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '创建任务失败',
      });
      throw error;
    }
  },

  // 更新任务
  updateTask: async (id: string, updates: Partial<Task>) => {
    try {
      const response = await apiClient.put(`/api/tasks/${id}`, updates);
      console.log('Update task response:', response);
      
      const updatedTask = response.data || response;
      
      set((state) => ({
        tasks: state.tasks.map((task) =>
          task.id === id ? { ...task, ...updatedTask } : task
        ),
        activeTasks: state.activeTasks.map((task) =>
          task.id === id ? { ...task, ...updatedTask } : task
        ),
      }));
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : '更新任务失败',
      });
      throw error;
    }
  },

  // 删除任务
  deleteTask: async (id: string) => {
    try {
      const response = await apiClient.delete(`/api/tasks/${id}`);
      console.log('Delete task response:', response);
      
      set((state) => ({
        tasks: state.tasks.filter((task) => task.id !== id),
        activeTasks: state.activeTasks.filter((task) => task.id !== id),
      }));
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : '删除任务失败',
      });
      throw error;
    }
  },

  // 启动任务
  startTask: async (id: string) => {
    await get().updateTask(id, { status: 'running' as TaskStatus });
  },

  // 暂停任务
  pauseTask: async (id: string) => {
    await get().updateTask(id, { status: 'paused' as TaskStatus });
  },

  // 停止任务
  stopTask: async (id: string) => {
    await get().updateTask(id, { status: 'cancelled' as TaskStatus });
  },

  // 更新任务进度
  updateTaskProgress: (id: string, progress: number) => {
    set((state) => ({
      tasks: state.tasks.map((task) =>
        task.id === id ? { ...task, progress } : task
      ),
      activeTasks: state.activeTasks.map((task) =>
        task.id === id ? { ...task, progress } : task
      ),
    }));
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 按类型获取任务
  getTasksByType: (type: TaskType) => {
    return get().tasks.filter((task) => task.type === type);
  },

  // 按状态获取任务
  getTasksByStatus: (status: TaskStatus) => {
    return get().tasks.filter((task) => task.status === status);
  },
}));
