"""
数据库模型和初始化
"""

import os
import json
from datetime import datetime
from typing import Optional, Dict, Any, List
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime, Float, JSON
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from werkzeug.security import generate_password_hash, check_password_hash

# 创建数据库实例
db = SQLAlchemy()

class User(db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    email = Column(String(120), unique=True, nullable=True)
    role = Column(String(20), default='user')
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime, nullable=True)
    
    def set_password(self, password: str):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password: str) -> bool:
        """检查密码"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
        }

class TelegramAccount(db.Model):
    """Telegram账号模型"""
    __tablename__ = 'telegram_accounts'
    
    id = Column(String(50), primary_key=True)
    name = Column(String(100), nullable=False)
    phone = Column(String(20), nullable=False)
    user_id = Column(Integer, nullable=True)
    username = Column(String(50), nullable=True)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True)
    session_data = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    
    # 关联任务
    tasks = relationship("Task", back_populates="account")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'phone': self.phone,
            'user_id': self.user_id,
            'username': self.username,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
        }

class TelegramGroup(db.Model):
    """Telegram群组模型"""
    __tablename__ = 'telegram_groups'
    
    id = Column(String(50), primary_key=True)
    title = Column(String(200), nullable=False)
    username = Column(String(100), nullable=True)
    type = Column(String(20), nullable=False)  # group, supergroup, channel
    member_count = Column(Integer, nullable=True)
    description = Column(Text, nullable=True)
    account_id = Column(String(50), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'username': self.username,
            'type': self.type,
            'member_count': self.member_count,
            'description': self.description,
            'account_id': self.account_id,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }

class Task(db.Model):
    """任务模型"""
    __tablename__ = 'tasks'
    
    id = Column(String(50), primary_key=True)
    name = Column(String(200), nullable=False)
    type = Column(String(20), nullable=False)  # download, forward, listen_forward
    status = Column(String(20), default='pending')  # pending, running, paused, completed, failed, cancelled
    account_id = Column(String(50), nullable=False)
    source_group_id = Column(String(50), nullable=False)
    target_group_id = Column(String(50), nullable=True)
    start_message_id = Column(Integer, nullable=True)
    end_message_id = Column(Integer, nullable=True)
    file_types = Column(JSON, nullable=True)
    download_path = Column(String(500), nullable=True)
    filter_text = Column(Text, nullable=True)
    progress = Column(Float, default=0.0)
    total_files = Column(Integer, default=0)
    completed_files = Column(Integer, default=0)
    failed_files = Column(Integer, default=0)
    download_speed = Column(String(20), nullable=True)
    error_message = Column(Text, nullable=True)
    config = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # 关联账号
    account = relationship("TelegramAccount", back_populates="tasks")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'type': self.type,
            'status': self.status,
            'accountId': self.account_id,
            'sourceGroupId': self.source_group_id,
            'targetGroupId': self.target_group_id,
            'startMessageId': self.start_message_id,
            'endMessageId': self.end_message_id,
            'fileTypes': self.file_types,
            'downloadPath': self.download_path,
            'filterText': self.filter_text,
            'progress': self.progress,
            'totalFiles': self.total_files,
            'completedFiles': self.completed_files,
            'failedFiles': self.failed_files,
            'downloadSpeed': self.download_speed,
            'error': self.error_message,
            'config': self.config,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
            'startedAt': self.started_at.isoformat() if self.started_at else None,
            'completedAt': self.completed_at.isoformat() if self.completed_at else None,
        }

class ForwardRule(db.Model):
    """转发规则模型"""
    __tablename__ = 'forward_rules'
    
    id = Column(String(50), primary_key=True)
    name = Column(String(200), nullable=False)
    source_pattern = Column(Text, nullable=False)
    target_template = Column(Text, nullable=False)
    enabled = Column(Boolean, default=True)
    priority = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'sourcePattern': self.source_pattern,
            'targetTemplate': self.target_template,
            'enabled': self.enabled,
            'priority': self.priority,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
        }

class SystemSettings(db.Model):
    """系统设置模型"""
    __tablename__ = 'system_settings'
    
    id = Column(Integer, primary_key=True)
    key = Column(String(100), unique=True, nullable=False)
    value = Column(Text, nullable=True)
    type = Column(String(20), default='string')  # string, integer, float, boolean, json
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def get_value(self):
        """获取类型化的值"""
        if self.value is None:
            return None
        
        if self.type == 'integer':
            return int(self.value)
        elif self.type == 'float':
            return float(self.value)
        elif self.type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes', 'on')
        elif self.type == 'json':
            return json.loads(self.value)
        else:
            return self.value
    
    def set_value(self, value):
        """设置类型化的值"""
        if value is None:
            self.value = None
        elif self.type == 'json':
            self.value = json.dumps(value)
        else:
            self.value = str(value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'key': self.key,
            'value': self.get_value(),
            'type': self.type,
            'description': self.description,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
        }

class TaskLog(db.Model):
    """任务日志模型"""
    __tablename__ = 'task_logs'
    
    id = Column(Integer, primary_key=True)
    task_id = Column(String(50), nullable=False)
    level = Column(String(10), nullable=False)  # DEBUG, INFO, WARNING, ERROR
    message = Column(Text, nullable=False)
    details = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'taskId': self.task_id,
            'level': self.level,
            'message': self.message,
            'details': self.details,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
        }

def init_db(app: Flask):
    """初始化数据库"""
    db.init_app(app)
    
    with app.app_context():
        # 创建所有表
        db.create_all()
        
        # 创建默认用户
        create_default_user()
        
        # 创建默认设置
        create_default_settings()

def create_default_user():
    """创建默认用户"""
    if not User.query.filter_by(username='admin').first():
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            role='admin'
        )
        admin_user.set_password('admin')
        db.session.add(admin_user)
        db.session.commit()
        print("Created default admin user (username: admin, password: admin)")

def create_default_settings():
    """创建默认设置"""
    default_settings = [
        ('download_path', './downloads', 'string', '默认下载路径'),
        ('max_concurrent_tasks', '3', 'integer', '最大并发任务数'),
        ('theme', 'light', 'string', '界面主题'),
        ('language', 'zh-CN', 'string', '界面语言'),
        ('notification_enabled', 'true', 'boolean', '启用通知'),
        ('proxy_enabled', 'false', 'boolean', '启用代理'),
        ('proxy_settings', '{}', 'json', '代理设置'),
    ]
    
    for key, value, type_, description in default_settings:
        if not SystemSettings.query.filter_by(key=key).first():
            setting = SystemSettings(
                key=key,
                value=value,
                type=type_,
                description=description
            )
            db.session.add(setting)
    
    db.session.commit()

def get_setting(key: str, default=None):
    """获取设置值"""
    setting = SystemSettings.query.filter_by(key=key).first()
    if setting:
        return setting.get_value()
    return default

def set_setting(key: str, value, type_: str = 'string', description: str = ''):
    """设置值"""
    setting = SystemSettings.query.filter_by(key=key).first()
    if setting:
        setting.set_value(value)
        setting.updated_at = datetime.utcnow()
    else:
        setting = SystemSettings(
            key=key,
            type=type_,
            description=description
        )
        setting.set_value(value)
        db.session.add(setting)
    
    db.session.commit()
    return setting
