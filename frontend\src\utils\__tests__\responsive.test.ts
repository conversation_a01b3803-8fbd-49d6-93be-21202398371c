import { describe, it, expect, beforeEach, vi } from 'vitest'
import { 
  getCurrentBreakpoint, 
  isMobile, 
  isTablet, 
  isDesktop,
  getResponsiveGrid,
  breakpoints 
} from '../responsive'

describe('responsive utils', () => {
  describe('getCurrentBreakpoint', () => {
    it('should return correct breakpoint for different widths', () => {
      expect(getCurrentBreakpoint(500)).toBe('xs')
      expect(getCurrentBreakpoint(600)).toBe('sm')
      expect(getCurrentBreakpoint(800)).toBe('md')
      expect(getCurrentBreakpoint(1000)).toBe('lg')
      expect(getCurrentBreakpoint(1300)).toBe('xl')
      expect(getCurrentBreakpoint(1700)).toBe('xxl')
    })
  })

  describe('device type detection', () => {
    it('should correctly identify mobile devices', () => {
      expect(isMobile(500)).toBe(true)
      expect(isMobile(767)).toBe(true)
      expect(isMobile(768)).toBe(false)
    })

    it('should correctly identify tablet devices', () => {
      expect(isTablet(767)).toBe(false)
      expect(isTablet(768)).toBe(true)
      expect(isTablet(991)).toBe(true)
      expect(isTablet(992)).toBe(false)
    })

    it('should correctly identify desktop devices', () => {
      expect(isDesktop(991)).toBe(false)
      expect(isDesktop(992)).toBe(true)
      expect(isDesktop(1500)).toBe(true)
    })
  })

  describe('getResponsiveGrid', () => {
    it('should return correct grid configuration', () => {
      const grid = getResponsiveGrid(24, 12, 8)
      
      expect(grid).toEqual({
        xs: 24,
        sm: 24,
        md: 12,
        lg: 8,
        xl: 8,
        xxl: 8,
      })
    })
  })
})

// Mock window for useResponsive hook tests
const mockWindow = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  })
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  })
}

describe('useResponsive hook', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should handle window resize events', () => {
    mockWindow(1200, 800)
    
    // Test would require rendering a component that uses the hook
    // This is a simplified test structure
    expect(window.innerWidth).toBe(1200)
    expect(window.innerHeight).toBe(800)
  })
})
