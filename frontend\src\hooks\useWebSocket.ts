import { useEffect, useRef, useState } from 'react';
import { useTaskStore } from '@/store/taskStore';
import { WS_EVENTS } from '@/utils/constants';

interface UseWebSocketOptions {
  url?: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export const useWebSocket = (options: UseWebSocketOptions = {}) => {
  const {
    url = 'ws://localhost:5000/ws',
    reconnectInterval = 3000,
    maxReconnectAttempts = 5,
  } = options;

  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { updateTaskProgress } = useTaskStore();

  const connect = () => {
    try {
      const ws = new WebSocket(url);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setError(null);
        reconnectAttemptsRef.current = 0;
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleMessage(data);
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };

      ws.onclose = () => {
        console.log('WebSocket disconnected');
        setIsConnected(false);
        wsRef.current = null;
        
        // 尝试重连
        if (reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current++;
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        } else {
          setError('WebSocket连接失败，已达到最大重连次数');
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setError('WebSocket连接错误');
      };
    } catch (err) {
      console.error('Failed to create WebSocket connection:', err);
      setError('无法创建WebSocket连接');
    }
  };

  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setIsConnected(false);
  };

  const sendMessage = (message: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  };

  const handleMessage = (data: any) => {
    switch (data.type) {
      case WS_EVENTS.TASK_PROGRESS:
        if (data.taskId && typeof data.progress === 'number') {
          updateTaskProgress(data.taskId, data.progress);
        }
        break;
      case WS_EVENTS.TASK_UPDATE:
        // TODO: 处理任务状态更新
        console.log('Task update:', data);
        break;
      case WS_EVENTS.SYSTEM_STATUS:
        // TODO: 处理系统状态更新
        console.log('System status:', data);
        break;
      case WS_EVENTS.ERROR:
        console.error('WebSocket error message:', data.message);
        setError(data.message);
        break;
      default:
        console.log('Unknown WebSocket message type:', data.type);
    }
  };

  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, [url]);

  return {
    isConnected,
    error,
    sendMessage,
    connect,
    disconnect,
  };
};
