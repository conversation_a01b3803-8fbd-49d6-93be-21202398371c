// 可访问性工具函数

// 检查颜色对比度
export const checkColorContrast = (foreground: string, background: string): number => {
  // 将颜色转换为RGB值
  const getRGB = (color: string) => {
    const hex = color.replace('#', '');
    return {
      r: parseInt(hex.substr(0, 2), 16),
      g: parseInt(hex.substr(2, 2), 16),
      b: parseInt(hex.substr(4, 2), 16),
    };
  };

  // 计算相对亮度
  const getLuminance = (rgb: { r: number; g: number; b: number }) => {
    const { r, g, b } = rgb;
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  };

  const fg = getRGB(foreground);
  const bg = getRGB(background);
  
  const fgLuminance = getLuminance(fg);
  const bgLuminance = getLuminance(bg);
  
  const lighter = Math.max(fgLuminance, bgLuminance);
  const darker = Math.min(fgLuminance, bgLuminance);
  
  return (lighter + 0.05) / (darker + 0.05);
};

// 检查对比度是否符合WCAG标准
export const isContrastCompliant = (
  foreground: string, 
  background: string, 
  level: 'AA' | 'AAA' = 'AA',
  size: 'normal' | 'large' = 'normal'
): boolean => {
  const contrast = checkColorContrast(foreground, background);
  
  if (level === 'AAA') {
    return size === 'large' ? contrast >= 4.5 : contrast >= 7;
  } else {
    return size === 'large' ? contrast >= 3 : contrast >= 4.5;
  }
};

// 键盘导航检查
export const checkKeyboardNavigation = (): string[] => {
  const issues: string[] = [];
  
  // 检查所有可交互元素是否可以通过键盘访问
  const interactiveElements = document.querySelectorAll(
    'button, a, input, select, textarea, [tabindex], [role="button"], [role="link"]'
  );
  
  interactiveElements.forEach((element, index) => {
    const tabIndex = element.getAttribute('tabindex');
    
    // 检查是否有负的tabindex（除了-1）
    if (tabIndex && parseInt(tabIndex) < -1) {
      issues.push(`Element ${index} has invalid tabindex: ${tabIndex}`);
    }
    
    // 检查是否有焦点样式
    const computedStyle = window.getComputedStyle(element as Element);
    if (!computedStyle.outline && !computedStyle.boxShadow) {
      issues.push(`Element ${index} may lack focus indicator`);
    }
  });
  
  return issues;
};

// 检查图片alt文本
export const checkImageAltText = (): string[] => {
  const issues: string[] = [];
  const images = document.querySelectorAll('img');
  
  images.forEach((img, index) => {
    const alt = img.getAttribute('alt');
    const src = img.getAttribute('src');
    
    if (!alt && src && !src.includes('data:')) {
      issues.push(`Image ${index} (${src}) is missing alt text`);
    }
    
    if (alt && alt.length > 125) {
      issues.push(`Image ${index} alt text is too long (${alt.length} characters)`);
    }
  });
  
  return issues;
};

// 检查表单标签
export const checkFormLabels = (): string[] => {
  const issues: string[] = [];
  const formControls = document.querySelectorAll('input, select, textarea');
  
  formControls.forEach((control, index) => {
    const id = control.getAttribute('id');
    const ariaLabel = control.getAttribute('aria-label');
    const ariaLabelledby = control.getAttribute('aria-labelledby');
    
    if (id) {
      const label = document.querySelector(`label[for="${id}"]`);
      if (!label && !ariaLabel && !ariaLabelledby) {
        issues.push(`Form control ${index} has no associated label`);
      }
    } else if (!ariaLabel && !ariaLabelledby) {
      issues.push(`Form control ${index} has no id and no aria-label`);
    }
  });
  
  return issues;
};

// 检查标题层次结构
export const checkHeadingStructure = (): string[] => {
  const issues: string[] = [];
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  
  let previousLevel = 0;
  
  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1));
    
    if (index === 0 && level !== 1) {
      issues.push('Page should start with h1');
    }
    
    if (level > previousLevel + 1) {
      issues.push(`Heading level skipped: h${previousLevel} to h${level} at index ${index}`);
    }
    
    previousLevel = level;
  });
  
  return issues;
};

// 检查ARIA属性
export const checkAriaAttributes = (): string[] => {
  const issues: string[] = [];
  const elementsWithAria = document.querySelectorAll('[aria-*]');
  
  elementsWithAria.forEach((element, index) => {
    const attributes = element.getAttributeNames().filter(name => name.startsWith('aria-'));
    
    attributes.forEach(attr => {
      const value = element.getAttribute(attr);
      
      // 检查boolean属性
      if (['aria-hidden', 'aria-expanded', 'aria-checked'].includes(attr)) {
        if (!['true', 'false'].includes(value || '')) {
          issues.push(`Element ${index} has invalid ${attr} value: ${value}`);
        }
      }
      
      // 检查必需的属性
      if (attr === 'aria-labelledby' || attr === 'aria-describedby') {
        const ids = value?.split(' ') || [];
        ids.forEach(id => {
          if (!document.getElementById(id)) {
            issues.push(`Element ${index} references non-existent id in ${attr}: ${id}`);
          }
        });
      }
    });
  });
  
  return issues;
};

// 综合可访问性检查
export const runAccessibilityAudit = () => {
  const results = {
    keyboardNavigation: checkKeyboardNavigation(),
    imageAltText: checkImageAltText(),
    formLabels: checkFormLabels(),
    headingStructure: checkHeadingStructure(),
    ariaAttributes: checkAriaAttributes(),
  };
  
  const totalIssues = Object.values(results).reduce((sum, issues) => sum + issues.length, 0);
  
  return {
    ...results,
    summary: {
      totalIssues,
      passed: totalIssues === 0,
      score: Math.max(0, 100 - totalIssues * 5), // 每个问题扣5分
    },
  };
};

// 焦点管理工具
export class FocusManager {
  private focusStack: HTMLElement[] = [];
  
  // 保存当前焦点
  saveFocus() {
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement) {
      this.focusStack.push(activeElement);
    }
  }
  
  // 恢复焦点
  restoreFocus() {
    const element = this.focusStack.pop();
    if (element && element.focus) {
      element.focus();
    }
  }
  
  // 设置焦点陷阱
  trapFocus(container: HTMLElement) {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
    };
    
    container.addEventListener('keydown', handleTabKey);
    
    // 返回清理函数
    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }
}

// 屏幕阅读器公告
export const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.style.position = 'absolute';
  announcement.style.left = '-10000px';
  announcement.style.width = '1px';
  announcement.style.height = '1px';
  announcement.style.overflow = 'hidden';
  
  document.body.appendChild(announcement);
  announcement.textContent = message;
  
  // 清理
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

// 跳过链接组件
export const createSkipLink = (targetId: string, text: string = '跳到主内容') => {
  const skipLink = document.createElement('a');
  skipLink.href = `#${targetId}`;
  skipLink.textContent = text;
  skipLink.className = 'skip-link';
  
  // 样式
  skipLink.style.position = 'absolute';
  skipLink.style.top = '-40px';
  skipLink.style.left = '6px';
  skipLink.style.background = '#000';
  skipLink.style.color = '#fff';
  skipLink.style.padding = '8px';
  skipLink.style.textDecoration = 'none';
  skipLink.style.zIndex = '1000';
  
  skipLink.addEventListener('focus', () => {
    skipLink.style.top = '6px';
  });
  
  skipLink.addEventListener('blur', () => {
    skipLink.style.top = '-40px';
  });
  
  return skipLink;
};
