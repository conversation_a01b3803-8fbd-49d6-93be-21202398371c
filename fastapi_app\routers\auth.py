"""
认证相关API路由
"""

from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from ..auth import authenticate_user, create_user_token, get_current_active_user, User, Token
from ..database import get_db
from ..utils import create_response

router = APIRouter()


class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str
    password: str


class LoginResponse(BaseModel):
    """登录响应模型"""
    success: bool
    data: dict
    message: str
    timestamp: str


class UserResponse(BaseModel):
    """用户响应模型"""
    id: str
    username: str
    role: str
    isAuthenticated: bool = True


@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest, db: AsyncSession = Depends(get_db)):
    """用户登录"""
    try:
        user = authenticate_user(login_data.username, login_data.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 创建访问令牌
        token = create_user_token(user)
        
        # 构造用户数据
        user_data = UserResponse(
            id=user.id,
            username=user.username,
            role=user.role
        )
        
        return LoginResponse(
            success=True,
            data={
                "user": user_data.dict(),
                "token": token.access_token,
                "token_type": token.token_type,
                "expires_in": token.expires_in
            },
            message="登录成功",
            timestamp=datetime.utcnow().isoformat() + "Z"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )


@router.post("/logout")
async def logout(current_user: User = Depends(get_current_active_user)):
    """用户登出"""
    # 在JWT模式下，登出主要是客户端删除token
    # 服务端可以维护一个黑名单，但这里简化处理
    return create_response(
        success=True,
        message="登出成功"
    )


@router.get("/status")
async def auth_status(current_user: User = Depends(get_current_active_user)):
    """获取认证状态"""
    user_data = UserResponse(
        id=current_user.id,
        username=current_user.username,
        role=current_user.role
    )
    
    return create_response(
        success=True,
        data={
            "authenticated": True,
            "user": user_data.dict()
        }
    )


@router.get("/me")
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """获取当前用户信息"""
    user_data = UserResponse(
        id=current_user.id,
        username=current_user.username,
        role=current_user.role
    )
    
    return create_response(
        success=True,
        data=user_data.dict()
    )


# OAuth2兼容的登录端点
@router.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """OAuth2兼容的登录端点"""
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return create_user_token(user)
