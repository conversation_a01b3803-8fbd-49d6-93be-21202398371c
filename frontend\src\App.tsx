import React from 'react';
import { RouterProvider } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { router } from '@/router';
import { getTheme, getSystemTheme } from '@/utils/theme';
import ThemeSettings from '@/components/ThemeSettings';
import { measurePageLoad, generatePerformanceReport } from '@/utils/performance';

const App: React.FC = () => {
  const [themeMode, setThemeMode] = React.useState<'light' | 'dark' | 'auto'>(() => {
    const saved = localStorage.getItem('theme-mode');
    return (saved as 'light' | 'dark' | 'auto') || 'auto';
  });

  const [colorScheme, setColorScheme] = React.useState(() => {
    const saved = localStorage.getItem('color-scheme');
    return saved || 'blue';
  });

  const [themeSettingsVisible, setThemeSettingsVisible] = React.useState(false);
  const [modalKey, setModalKey] = React.useState(0);



  const getCurrentTheme = () => {
    let actualMode: 'light' | 'dark' = themeMode as 'light' | 'dark';
    if (themeMode === 'auto') {
      actualMode = getSystemTheme();
    }
    return getTheme(actualMode, colorScheme);
  };

  const getCurrentActualMode = () => {
    if (themeMode === 'auto') {
      return getSystemTheme();
    }
    return themeMode;
  };

  const handleThemeChange = (mode: 'light' | 'dark' | 'auto') => {
    setThemeMode(mode);
    localStorage.setItem('theme-mode', mode);

    // 为了兼容旧的主题切换逻辑
    const actualMode = mode === 'auto' ? getSystemTheme() : mode;
    localStorage.setItem('theme', actualMode);

    // 立即应用主题变化
    const theme = getTheme(actualMode, colorScheme);
    const root = document.documentElement;
    root.style.setProperty('--ant-color-primary', theme.token?.colorPrimary || '#1890ff');
    root.style.setProperty('--ant-color-bg-container', theme.token?.colorBgContainer || '#ffffff');
    root.style.setProperty('--ant-color-bg-layout', theme.token?.colorBgLayout || '#f5f5f5');
    root.style.setProperty('--ant-color-text', theme.token?.colorText || 'rgba(0, 0, 0, 0.88)');
    root.style.setProperty('--ant-color-text-secondary', theme.token?.colorTextSecondary || 'rgba(0, 0, 0, 0.65)');
    root.style.setProperty('--ant-color-border', theme.token?.colorBorder || '#d9d9d9');

    // 设置body的data-theme属性
    document.body.setAttribute('data-theme', actualMode);
    document.body.style.backgroundColor = theme.token?.colorBgLayout || '#f5f5f5';
    document.body.style.color = theme.token?.colorText || 'rgba(0, 0, 0, 0.88)';
  };

  const handleColorSchemeChange = (scheme: string) => {
    setColorScheme(scheme);
    localStorage.setItem('color-scheme', scheme);

    // 立即应用颜色方案变化
    const actualMode = getCurrentActualMode();
    const theme = getTheme(actualMode, scheme);
    const root = document.documentElement;
    root.style.setProperty('--ant-color-primary', theme.token?.colorPrimary || '#1890ff');
    root.style.setProperty('--ant-color-bg-container', theme.token?.colorBgContainer || '#ffffff');
    root.style.setProperty('--ant-color-bg-layout', theme.token?.colorBgLayout || '#f5f5f5');
    root.style.setProperty('--ant-color-text', theme.token?.colorText || 'rgba(0, 0, 0, 0.88)');
    root.style.setProperty('--ant-color-text-secondary', theme.token?.colorTextSecondary || 'rgba(0, 0, 0, 0.65)');
    root.style.setProperty('--ant-color-border', theme.token?.colorBorder || '#d9d9d9');

    document.body.style.backgroundColor = theme.token?.colorBgLayout || '#f5f5f5';
    document.body.style.color = theme.token?.colorText || 'rgba(0, 0, 0, 0.88)';
  };

  // 应用主题到body元素
  React.useEffect(() => {
    const actualMode = getCurrentActualMode();
    const theme = getCurrentTheme();

    // 设置body的data-theme属性
    document.body.setAttribute('data-theme', actualMode);

    // 设置CSS变量
    const root = document.documentElement;
    root.style.setProperty('--ant-color-primary', theme.token?.colorPrimary || '#1890ff');
    root.style.setProperty('--ant-color-bg-container', theme.token?.colorBgContainer || '#ffffff');
    root.style.setProperty('--ant-color-bg-layout', theme.token?.colorBgLayout || '#f5f5f5');
    root.style.setProperty('--ant-color-text', theme.token?.colorText || 'rgba(0, 0, 0, 0.88)');
    root.style.setProperty('--ant-color-text-secondary', theme.token?.colorTextSecondary || 'rgba(0, 0, 0, 0.65)');
    root.style.setProperty('--ant-color-border', theme.token?.colorBorder || '#d9d9d9');

    // 设置body背景色
    document.body.style.backgroundColor = theme.token?.colorBgLayout || '#f5f5f5';
    document.body.style.color = theme.token?.colorText || 'rgba(0, 0, 0, 0.88)';
  }, [themeMode, colorScheme]);

  React.useEffect(() => {
    // 监听系统主题变化
    if (themeMode === 'auto' && typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => {
        // 触发重新渲染
        setThemeMode('auto');
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [themeMode]);

  React.useEffect(() => {
    // 监听存储变化
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'theme-mode') {
        const newMode = (e.newValue as 'light' | 'dark' | 'auto') || 'auto';
        setThemeMode(newMode);
        // 立即应用主题变化
        const actualMode = newMode === 'auto' ? getSystemTheme() : newMode;
        const theme = getTheme(actualMode, colorScheme);
        const root = document.documentElement;
        root.style.setProperty('--ant-color-primary', theme.token?.colorPrimary || '#1890ff');
        root.style.setProperty('--ant-color-bg-container', theme.token?.colorBgContainer || '#ffffff');
        root.style.setProperty('--ant-color-bg-layout', theme.token?.colorBgLayout || '#f5f5f5');
        root.style.setProperty('--ant-color-text', theme.token?.colorText || 'rgba(0, 0, 0, 0.88)');
        root.style.setProperty('--ant-color-text-secondary', theme.token?.colorTextSecondary || 'rgba(0, 0, 0, 0.65)');
        root.style.setProperty('--ant-color-border', theme.token?.colorBorder || '#d9d9d9');
        document.body.setAttribute('data-theme', actualMode);
        document.body.style.backgroundColor = theme.token?.colorBgLayout || '#f5f5f5';
        document.body.style.color = theme.token?.colorText || 'rgba(0, 0, 0, 0.88)';
      } else if (e.key === 'color-scheme') {
        setColorScheme(e.newValue || 'blue');
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [colorScheme]);

  // 将主题设置函数传递给全局
  React.useEffect(() => {
    (window as any).openThemeSettings = () => {
      setThemeSettingsVisible(true);
      setModalKey(prev => prev + 1); // 强制重新渲染模态框
    };
  }, []);

  // 性能监控
  React.useEffect(() => {
    // 页面加载完成后进行性能监控
    const timer = setTimeout(() => {
      const performanceData = measurePageLoad();
      if (performanceData && process.env.NODE_ENV === 'development') {
        console.log('Page Performance:', performanceData);

        // 生成性能报告
        const report = generatePerformanceReport();
        console.log('Performance Report:', report);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <ConfigProvider
      locale={zhCN}
      theme={getCurrentTheme()}
    >


      <RouterProvider router={router} />

      {/* 主题设置模态框 */}
      {themeSettingsVisible && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1000,
            backgroundColor: 'rgba(0, 0, 0, 0.45)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onClick={() => setThemeSettingsVisible(false)}
        >
          <div
            style={{
              width: 600,
              maxWidth: '90vw',
              maxHeight: '90vh',
              backgroundColor: 'white',
              borderRadius: 8,
              boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
              overflow: 'hidden',
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 标题栏 */}
            <div
              style={{
                padding: '16px 24px',
                borderBottom: '1px solid #f0f0f0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <h4 style={{ margin: 0, fontSize: 16, fontWeight: 600 }}>主题设置</h4>
              <button
                onClick={() => setThemeSettingsVisible(false)}
                style={{
                  border: 'none',
                  background: 'none',
                  fontSize: 16,
                  cursor: 'pointer',
                  padding: 4,
                  borderRadius: 4,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                ✕
              </button>
            </div>
            {/* 内容区域 */}
            <div style={{ padding: 24, maxHeight: 'calc(90vh - 120px)', overflow: 'auto' }}>
              <ThemeSettings
                currentTheme={themeMode}
                currentColorScheme={colorScheme}
                onThemeChange={handleThemeChange}
                onColorSchemeChange={handleColorSchemeChange}
              />
            </div>
          </div>
        </div>
      )}
    </ConfigProvider>
  );
};

export default App;
