import React from 'react';
import { Select, Space, Avatar, Typography, Tag } from 'antd';
import { UserOutlined, CheckCircleOutlined } from '@ant-design/icons';
import type { Account } from '@/types';

const { Text } = Typography;

interface AccountSelectorProps {
  accounts: Account[];
  value?: string;
  onChange?: (accountId: string) => void;
  loading?: boolean;
  placeholder?: string;
}

const AccountSelector: React.FC<AccountSelectorProps> = ({
  accounts,
  value,
  onChange,
  loading = false,
  placeholder = '请选择账号',
}) => {
  const options = accounts.map((account) => ({
    label: (
      <Space>
        <Avatar size="small" icon={<UserOutlined />} />
        <div>
          <Text strong>{account.name}</Text>
          {account.phone && (
            <Text type="secondary" style={{ marginLeft: 8, fontSize: '12px' }}>
              {account.phone}
            </Text>
          )}
        </div>
        {account.isActive && (
          <Tag color="green" icon={<CheckCircleOutlined />}>
            在线
          </Tag>
        )}
      </Space>
    ),
    value: account.id,
    disabled: !account.isActive,
  }));

  return (
    <Select
      value={value}
      onChange={onChange}
      options={options}
      placeholder={placeholder}
      loading={loading}
      style={{ width: '100%' }}
      optionLabelProp="label"
      showSearch
      filterOption={(input, option) => {
        const account = accounts.find((acc) => acc.id === option?.value);
        if (!account) return false;
        return Boolean(
          account.name.toLowerCase().includes(input.toLowerCase()) ||
          (account.phone && account.phone.includes(input))
        );
      }}
    />
  );
};

export default AccountSelector;
