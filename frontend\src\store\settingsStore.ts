import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { SystemSettings } from '@/types';
import { DEFAULT_SETTINGS } from '@/utils/constants';
import { apiClient } from '@/services/api';

interface SettingsState {
  settings: SystemSettings;
  isLoading: boolean;
  error: string | null;
}

interface SettingsActions {
  fetchSettings: () => Promise<void>;
  updateSettings: (updates: Partial<SystemSettings>) => Promise<void>;
  resetSettings: () => void;
  clearError: () => void;
}

type SettingsStore = SettingsState & SettingsActions;

export const useSettingsStore = create<SettingsStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      settings: DEFAULT_SETTINGS,
      isLoading: false,
      error: null,

      // 获取设置
      fetchSettings: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiClient.get('/api/settings');
          console.log('Settings response:', response);
          
          const settings = { ...DEFAULT_SETTINGS, ...(response.data || response) };
          
          set({
            settings,
            isLoading: false,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : '获取设置失败',
          });
        }
      },

      // 更新设置
      updateSettings: async (updates: Partial<SystemSettings>) => {
        set({ isLoading: true, error: null });
        
        try {
          const currentSettings = get().settings;
          const newSettings = { ...currentSettings, ...updates };
          
          const response = await apiClient.put('/api/settings', newSettings);
          console.log('Update settings response:', response);
          
          set({
            settings: newSettings,
            isLoading: false,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : '更新设置失败',
          });
          throw error;
        }
      },

      // 重置设置
      resetSettings: () => {
        set({
          settings: DEFAULT_SETTINGS,
          error: null,
        });
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'settings-storage',
      partialize: (state) => ({
        settings: state.settings,
      }),
    }
  )
);
