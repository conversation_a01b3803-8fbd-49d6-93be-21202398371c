// API 端点
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
  },
  ACCOUNTS: {
    LIST: '/api/accounts',
    CREATE: '/api/accounts',
    DELETE: (id: string) => `/api/accounts/${id}`,
  },
  GROUPS: {
    LIST: '/api/groups',
    BY_ACCOUNT: (accountId: string) => `/api/groups?accountId=${accountId}`,
  },
  TASKS: {
    LIST: '/api/tasks',
    CREATE: '/api/tasks',
    UPDATE: (id: string) => `/api/tasks/${id}`,
    DELETE: (id: string) => `/api/tasks/${id}`,
    START: (id: string) => `/api/tasks/${id}/start`,
    PAUSE: (id: string) => `/api/tasks/${id}/pause`,
    STOP: (id: string) => `/api/tasks/${id}/stop`,
  },
  SETTINGS: {
    GET: '/api/settings',
    UPDATE: '/api/settings',
  },
  STATS: {
    OVERVIEW: '/api/stats/overview',
    DOWNLOAD_SPEED: '/api/stats/download-speed',
  },
} as const;

// WebSocket 事件
export const WS_EVENTS = {
  TASK_UPDATE: 'task_update',
  TASK_PROGRESS: 'task_progress',
  SYSTEM_STATUS: 'system_status',
  ERROR: 'error',
} as const;

// 本地存储键
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_SETTINGS: 'user_settings',
  THEME: 'theme',
  LANGUAGE: 'language',
} as const;

// 默认设置
export const DEFAULT_SETTINGS = {
  downloadPath: './downloads',
  maxConcurrentTasks: 3,
  theme: 'light' as const,
  language: 'zh-CN',
  telegramSettings: {
    apiId: 0,
    apiHash: '',
    sessionName: 'telegram_session',
    botToken: '',
    sessionPath: './sessions',
  },
  fileSettings: {
    maxFileSize: 2 * 1024 * 1024 * 1024, // 2GB
    chunkSize: 1024 * 1024, // 1MB
    filePathPrefix: ['chat_title', 'media_datetime'],
    hideFileName: false,
    fileNamePrefix: ['message_id', 'file_name'],
    mediaTypes: ['audio', 'document', 'photo', 'video', 'voice', 'animation'],
    fileFormats: {
      audio: ['all'],
      document: ['pdf', 'epub'],
      video: ['mp4'],
      photo: ['all'],
      voice: ['all'],
      animation: ['all']
    },
    enableDownloadTxt: false,
    dropNoAudioVideo: false,
  },
  performanceSettings: {
    timeout: 30,
    retryAttempts: 3,
    retryDelay: 5,
    maxDownloadSpeed: 0,
    maxConcurrentTransmissions: 1,
    startTimeout: 60,
  },
  securitySettings: {
    sessionTimeout: 3600,
    maxLoginAttempts: 5,
    loginAttemptTimeout: 300,
    allowedHosts: ['localhost', '127.0.0.1'],
    webLoginSecret: '',
    allowedUserIds: [],
  },
  databaseSettings: {
    url: 'sqlite:///app.db',
    echo: false,
    poolSize: 10,
    maxOverflow: 20,
  },
  proxySettings: {
    enabled: false,
    type: 'http' as const,
    host: '',
    port: 0,
    username: '',
    password: '',
  },
  notificationSettings: {
    enabled: true,
    taskCompletion: true,
    taskFailure: true,
    systemAlerts: true,
    emailEnabled: false,
    emailSmtpServer: '',
    emailSmtpPort: 587,
    emailUsername: '',
    emailPassword: '',
    emailFrom: '',
    emailTo: [],
  },
};

// 文件类型
export const FILE_TYPES = {
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'],
  VIDEO: ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'],
  AUDIO: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
  ARCHIVE: ['zip', 'rar', '7z', 'tar', 'gz'],
} as const;

// 任务状态颜色映射
export const TASK_STATUS_COLORS = {
  pending: 'default',
  running: 'processing',
  paused: 'warning',
  completed: 'success',
  failed: 'error',
  cancelled: 'default',
} as const;

// 任务类型图标映射
export const TASK_TYPE_ICONS = {
  download: 'DownloadOutlined',
  forward: 'ShareAltOutlined',
  listen_forward: 'SoundOutlined',
} as const;

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'],
} as const;

// 时间格式
export const DATE_FORMATS = {
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
} as const;
