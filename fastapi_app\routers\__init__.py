"""
FastAPI路由模块
"""

from fastapi import APIRouter
from .auth import router as auth_router
from .accounts import router as accounts_router
from .groups import router as groups_router
from .tasks import router as tasks_router
from .dashboard import router as dashboard_router
from .settings import router as settings_router

# 创建主路由器
api_router = APIRouter()

# 注册子路由
api_router.include_router(auth_router, prefix="/auth", tags=["认证"])
api_router.include_router(accounts_router, prefix="/accounts", tags=["账号管理"])
api_router.include_router(groups_router, prefix="/groups", tags=["群组管理"])
api_router.include_router(tasks_router, prefix="/tasks", tags=["任务管理"])
api_router.include_router(dashboard_router, prefix="/dashboard", tags=["仪表板"])
api_router.include_router(settings_router, prefix="/settings", tags=["系统设置"])
