"""
FastAPI配置模块
"""

import os
from typing import List, Optional
from functools import lru_cache
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    app_name: str = Field(default="Telegram Media Downloader", env="APP_NAME")
    debug: bool = Field(default=False, env="DEBUG")
    
    # 服务器配置
    host: str = Field(default="127.0.0.1", env="HOST")
    port: int = Field(default=8000, env="PORT")
    
    # 安全配置
    secret_key: str = Field(default="your-secret-key-change-in-production", env="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    
    # CORS配置
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:3001", "http://127.0.0.1:3001"],
        env="CORS_ORIGINS"
    )
    
    # 受信任主机
    trusted_hosts: Optional[List[str]] = Field(default=None, env="TRUSTED_HOSTS")
    
    # 数据库配置
    database_url: str = Field(
        default="sqlite:///./telegram_downloader.db",
        env="DATABASE_URL"
    )
    database_echo: bool = Field(default=False, env="DATABASE_ECHO")
    
    # Redis配置
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # 文件存储配置
    download_path: str = Field(default="./downloads", env="DOWNLOAD_PATH")
    max_file_size: int = Field(default=2 * 1024 * 1024 * 1024, env="MAX_FILE_SIZE")  # 2GB
    
    # Telegram配置
    telegram_api_id: Optional[int] = Field(default=None, env="TELEGRAM_API_ID")
    telegram_api_hash: Optional[str] = Field(default=None, env="TELEGRAM_API_HASH")
    telegram_session_path: str = Field(default="./sessions", env="TELEGRAM_SESSION_PATH")
    
    # WebSocket配置
    websocket_ping_interval: int = Field(default=20, env="WEBSOCKET_PING_INTERVAL")
    websocket_ping_timeout: int = Field(default=10, env="WEBSOCKET_PING_TIMEOUT")
    
    # 任务配置
    max_concurrent_downloads: int = Field(default=5, env="MAX_CONCURRENT_DOWNLOADS")
    download_timeout: int = Field(default=300, env="DOWNLOAD_TIMEOUT")  # 5分钟
    
    # 前端配置
    frontend_path: str = Field(default="./frontend/dist", env="FRONTEND_PATH")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        @classmethod
        def parse_env_var(cls, field_name: str, raw_val: str):
            """解析环境变量"""
            if field_name in ['cors_origins', 'trusted_hosts']:
                # 解析逗号分隔的列表
                return [x.strip() for x in raw_val.split(',') if x.strip()]
            return cls.json_loads(raw_val)


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()


def get_database_url() -> str:
    """获取数据库URL"""
    settings = get_settings()
    return settings.database_url


def get_redis_url() -> str:
    """获取Redis URL"""
    settings = get_settings()
    return settings.redis_url


def is_development() -> bool:
    """判断是否为开发环境"""
    return get_settings().debug


def get_cors_origins() -> List[str]:
    """获取CORS允许的源"""
    return get_settings().cors_origins


def get_secret_key() -> str:
    """获取密钥"""
    return get_settings().secret_key


def get_download_path() -> str:
    """获取下载路径"""
    settings = get_settings()
    download_path = settings.download_path
    
    # 确保下载目录存在
    os.makedirs(download_path, exist_ok=True)
    
    return download_path


def get_session_path() -> str:
    """获取会话文件路径"""
    settings = get_settings()
    session_path = settings.telegram_session_path
    
    # 确保会话目录存在
    os.makedirs(session_path, exist_ok=True)
    
    return session_path
