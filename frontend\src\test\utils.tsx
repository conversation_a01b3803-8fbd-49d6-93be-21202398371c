import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { ConfigProvider } from 'antd'
import { <PERSON>rowserRouter } from 'react-router-dom'
import zhCN from 'antd/locale/zh_CN'
import { lightTheme } from '@/utils/theme'

// 自定义渲染函数
const AllTheProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <BrowserRouter>
      <ConfigProvider locale={zhCN} theme={lightTheme}>
        {children}
      </ConfigProvider>
    </BrowserRouter>
  )
}

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Mock数据生成器
export const mockTask = (overrides = {}) => ({
  id: 'test-task-1',
  type: 'download',
  status: 'pending',
  accountId: 'test-account-1',
  sourceGroupId: 'test-group-1',
  targetGroupId: null,
  startMessageId: 1,
  endMessageId: 100,
  progress: 0,
  totalFiles: 0,
  completedFiles: 0,
  failedFiles: 0,
  downloadSpeed: null,
  createdAt: '2024-01-15T10:30:00.000Z',
  updatedAt: '2024-01-15T10:30:00.000Z',
  error: null,
  ...overrides,
})

export const mockAccount = (overrides = {}) => ({
  id: 'test-account-1',
  name: '测试账号',
  phone: '+86 138****1234',
  isActive: true,
  lastLogin: '2024-01-15T10:30:00.000Z',
  ...overrides,
})

export const mockGroup = (overrides = {}) => ({
  id: 'test-group-1',
  title: '测试群组',
  type: 'supergroup',
  memberCount: 1000,
  description: '这是一个测试群组',
  ...overrides,
})

export const mockSettings = (overrides = {}) => ({
  downloadPath: './downloads',
  maxConcurrentTasks: 3,
  theme: 'light',
  language: 'zh-CN',
  notificationSettings: {
    enabled: true,
    taskCompletion: true,
    taskFailure: true,
    systemAlerts: true,
  },
  ...overrides,
})

// 测试工具函数
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0))
}

export const createMockStore = (initialState = {}) => {
  const store = {
    getState: () => initialState,
    subscribe: vi.fn(),
    dispatch: vi.fn(),
  }
  return store
}

// 模拟API响应
export const mockApiResponse = (data: any, success = true) => ({
  success,
  data,
  timestamp: new Date().toISOString(),
  message: success ? 'Success' : 'Error',
  error: success ? null : 'Mock error',
})

// 模拟异步操作
export const mockAsyncOperation = (result: any, delay = 100) => {
  return new Promise(resolve => {
    setTimeout(() => resolve(result), delay)
  })
}
