// 基础类型定义
export interface User {
  id: string;
  username: string;
  isAuthenticated: boolean;
}

export interface Account {
  id: string;
  name: string;
  phone?: string;
  isActive: boolean;
  lastLogin?: string;
}

export interface Group {
  id: string;
  title: string;
  type: 'channel' | 'group' | 'supergroup';
  memberCount?: number;
  description?: string;
}

export interface Task {
  id: string;
  type: TaskType;
  status: TaskStatus;
  accountId: string;
  sourceGroupId?: string;
  targetGroupId?: string;
  startMessageId?: number;
  endMessageId?: number;
  progress: number;
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  downloadSpeed?: string;
  createdAt: string;
  updatedAt: string;
  error?: string;
}

export enum TaskType {
  DOWNLOAD = 'download',
  FORWARD = 'forward',
  LISTEN_FORWARD = 'listen_forward',
}

export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export interface DownloadTask extends Task {
  type: TaskType.DOWNLOAD;
  downloadPath: string;
  fileTypes: string[];
  filter?: string;
}

export interface ForwardTask extends Task {
  type: TaskType.FORWARD;
  forwardRules?: ForwardRule[];
}

export interface ListenForwardTask extends Task {
  type: TaskType.LISTEN_FORWARD;
  isListening: boolean;
  forwardRules?: ForwardRule[];
}

export interface ForwardRule {
  id: string;
  name: string;
  sourcePattern?: string;
  targetPattern?: string;
  isActive: boolean;
}

export interface SystemSettings {
  downloadPath: string;
  maxConcurrentTasks: number;
  proxySettings?: ProxySettings;
  notificationSettings: NotificationSettings;
  telegramSettings: TelegramSettings;
  fileSettings: FileSettings;
  performanceSettings: PerformanceSettings;
  securitySettings: SecuritySettings;
  databaseSettings: DatabaseSettings;
  theme: 'light' | 'dark' | 'auto';
  language: string;
}

export interface TelegramSettings {
  apiId: number;
  apiHash: string;
  sessionName: string;
  botToken?: string;
  sessionPath: string;
}

export interface FileSettings {
  maxFileSize: number; // bytes
  chunkSize: number; // bytes
  filePathPrefix: string[];
  hideFileName: boolean;
  fileNamePrefix: string[];
  mediaTypes: string[];
  fileFormats: Record<string, string[]>;
  enableDownloadTxt: boolean;
  dropNoAudioVideo: boolean;
}

export interface PerformanceSettings {
  timeout: number; // seconds
  retryAttempts: number;
  retryDelay: number; // seconds
  maxDownloadSpeed: number; // KB/s, 0 = unlimited
  maxConcurrentTransmissions: number;
  startTimeout: number; // seconds
}

export interface SecuritySettings {
  sessionTimeout: number; // seconds
  maxLoginAttempts: number;
  loginAttemptTimeout: number; // seconds
  allowedHosts: string[];
  webLoginSecret: string;
  allowedUserIds: string[];
}

export interface DatabaseSettings {
  url: string;
  echo: boolean;
  poolSize: number;
  maxOverflow: number;
}

export interface ProxySettings {
  enabled: boolean;
  type: 'http' | 'socks5';
  host: string;
  port: number;
  username?: string;
  password?: string;
}

export interface NotificationSettings {
  enabled: boolean;
  taskCompletion: boolean;
  taskFailure: boolean;
  systemAlerts: boolean;
  emailEnabled: boolean;
  emailSmtpServer: string;
  emailSmtpPort: number;
  emailUsername: string;
  emailPassword: string;
  emailFrom: string;
  emailTo: string[];
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}
