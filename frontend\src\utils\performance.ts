// 性能监控工具

// 页面加载性能监控
export const measurePageLoad = () => {
  if (typeof window !== 'undefined' && 'performance' in window) {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    return {
      // DNS查询时间
      dnsTime: navigation.domainLookupEnd - navigation.domainLookupStart,
      // TCP连接时间
      tcpTime: navigation.connectEnd - navigation.connectStart,
      // 请求响应时间
      requestTime: navigation.responseEnd - navigation.requestStart,
      // DOM解析时间
      domParseTime: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      // 页面加载完成时间
      loadTime: navigation.loadEventEnd - navigation.loadEventStart,
      // 首次内容绘制时间
      fcp: getFCP(),
      // 最大内容绘制时间
      lcp: getLCP(),
      // 首次输入延迟
      fid: getFID(),
      // 累积布局偏移
      cls: getCLS(),
    };
  }
  return null;
};

// 获取首次内容绘制时间 (FCP)
export const getFCP = (): number | null => {
  if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
    return new Promise((resolve) => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
        if (fcpEntry) {
          resolve(fcpEntry.startTime);
          observer.disconnect();
        }
      });
      observer.observe({ entryTypes: ['paint'] });
    }) as any;
  }
  return null;
};

// 获取最大内容绘制时间 (LCP)
export const getLCP = (): number | null => {
  if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
    return new Promise((resolve) => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        resolve(lastEntry.startTime);
      });
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    }) as any;
  }
  return null;
};

// 获取首次输入延迟 (FID)
export const getFID = (): number | null => {
  if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
    return new Promise((resolve) => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const firstEntry = entries[0];
        resolve(firstEntry.processingStart - firstEntry.startTime);
        observer.disconnect();
      });
      observer.observe({ entryTypes: ['first-input'] });
    }) as any;
  }
  return null;
};

// 获取累积布局偏移 (CLS)
export const getCLS = (): number | null => {
  if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
    let clsValue = 0;
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
    });
    observer.observe({ entryTypes: ['layout-shift'] });
    
    return clsValue;
  }
  return null;
};

// 内存使用监控
export const getMemoryUsage = () => {
  if (typeof window !== 'undefined' && 'performance' in window && 'memory' in performance) {
    const memory = (performance as any).memory;
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
    };
  }
  return null;
};

// 资源加载性能监控
export const getResourceTiming = () => {
  if (typeof window !== 'undefined' && 'performance' in window) {
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    return resources.map(resource => ({
      name: resource.name,
      duration: resource.duration,
      size: resource.transferSize,
      type: getResourceType(resource.name),
      cached: resource.transferSize === 0,
    }));
  }
  return [];
};

// 获取资源类型
const getResourceType = (url: string): string => {
  if (url.includes('.js')) return 'script';
  if (url.includes('.css')) return 'stylesheet';
  if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image';
  if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font';
  return 'other';
};

// 组件渲染性能监控
export const measureComponentRender = (componentName: string) => {
  const startTime = performance.now();
  
  return {
    end: () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      console.log(`Component ${componentName} render time: ${duration.toFixed(2)}ms`);
      
      // 如果渲染时间超过16ms（60fps），发出警告
      if (duration > 16) {
        console.warn(`Slow component render detected: ${componentName} took ${duration.toFixed(2)}ms`);
      }
      
      return duration;
    },
  };
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// 图片懒加载
export const lazyLoadImage = (img: HTMLImageElement, src: string) => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          img.src = src;
          img.classList.remove('lazy');
          observer.unobserve(img);
        }
      });
    },
    {
      rootMargin: '50px',
    }
  );
  
  observer.observe(img);
};

// 虚拟滚动优化
export const useVirtualScroll = (
  items: any[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = React.useState(0);
  
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );
  
  const visibleItems = items.slice(startIndex, endIndex + 1);
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;
  
  return {
    visibleItems,
    totalHeight,
    offsetY,
    setScrollTop,
  };
};

// 缓存管理
class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  set(key: string, data: any, ttl: number = 5 * 60 * 1000) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }
  
  get(key: string) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  clear() {
    this.cache.clear();
  }
  
  size() {
    return this.cache.size;
  }
}

export const cacheManager = new CacheManager();

// 性能报告
export const generatePerformanceReport = () => {
  const pageLoad = measurePageLoad();
  const memory = getMemoryUsage();
  const resources = getResourceTiming();
  
  return {
    timestamp: new Date().toISOString(),
    pageLoad,
    memory,
    resources: {
      total: resources.length,
      scripts: resources.filter(r => r.type === 'script').length,
      stylesheets: resources.filter(r => r.type === 'stylesheet').length,
      images: resources.filter(r => r.type === 'image').length,
      cached: resources.filter(r => r.cached).length,
    },
    recommendations: generateRecommendations(pageLoad, memory, resources),
  };
};

// 生成性能优化建议
const generateRecommendations = (pageLoad: any, memory: any, resources: any[]) => {
  const recommendations: string[] = [];
  
  if (pageLoad?.loadTime > 3000) {
    recommendations.push('页面加载时间过长，建议优化资源加载');
  }
  
  if (memory?.usagePercentage > 80) {
    recommendations.push('内存使用率过高，建议检查内存泄漏');
  }
  
  const largeResources = resources.filter(r => r.size > 1024 * 1024); // 1MB
  if (largeResources.length > 0) {
    recommendations.push('存在大型资源文件，建议进行压缩或分割');
  }
  
  const uncachedResources = resources.filter(r => !r.cached);
  if (uncachedResources.length > resources.length * 0.5) {
    recommendations.push('缓存利用率较低，建议优化缓存策略');
  }
  
  return recommendations;
};
