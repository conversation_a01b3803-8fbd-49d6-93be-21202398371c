import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  Divider,
  message,
  Select,
  InputNumber,
  Checkbox,
  List,
} from 'antd';
import { PlusOutlined, DownloadOutlined } from '@ant-design/icons';
import AccountSelector from '@/components/AccountSelector';
import GroupSelector from '@/components/GroupSelector';
import TaskCard from '@/components/TaskCard';
import { useTaskStore } from '@/store/taskStore';
import type { Account, Group, DownloadTask, TaskType } from '@/types';
import { FILE_TYPES } from '@/utils/constants';
import { useResponsive, getResponsiveGrid, getResponsiveLayout } from '@/utils/responsive';

const { Title, Text } = Typography;
const { TextArea } = Input;

const Download: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [groups, setGroups] = useState<Group[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<string>();
  const { isMobile, isTablet } = useResponsive();
  const layoutConfig = getResponsiveLayout();

  const {
    tasks,
    isLoading: tasksLoading,
    createTask,
    startTask,
    pauseTask,
    stopTask,
    deleteTask,
    fetchTasks,
    getTasksByType,
  } = useTaskStore();

  const downloadTasks = getTasksByType('download' as TaskType);

  // 模拟数据
  useEffect(() => {
    // 模拟账号数据
    setAccounts([
      {
        id: '1',
        name: '主账号',
        phone: '+86 138****1234',
        isActive: true,
        lastLogin: '2024-01-15 10:30:00',
      },
      {
        id: '2',
        name: '备用账号',
        phone: '+86 139****5678',
        isActive: false,
        lastLogin: '2024-01-14 15:20:00',
      },
    ]);

    // 获取任务列表
    fetchTasks();
  }, [fetchTasks]);

  // 当选择账号时，获取该账号的群组列表
  useEffect(() => {
    if (selectedAccount) {
      // 模拟群组数据
      setGroups([
        {
          id: 'group1',
          title: '技术交流群',
          type: 'supergroup',
          memberCount: 1250,
          description: '技术讨论和资源分享',
        },
        {
          id: 'channel1',
          title: '资源分享频道',
          type: 'channel',
          memberCount: 5680,
          description: '各种学习资源和工具分享',
        },
        {
          id: 'group2',
          title: '项目协作群',
          type: 'group',
          memberCount: 45,
          description: '项目开发协作讨论',
        },
      ]);
    } else {
      setGroups([]);
    }
  }, [selectedAccount]);

  const handleCreateTask = async (values: any) => {
    setLoading(true);
    try {
      const taskData: Partial<DownloadTask> = {
        type: 'download' as TaskType,
        accountId: values.accountId,
        sourceGroupId: values.groupId,
        startMessageId: values.startMessageId,
        endMessageId: values.endMessageId,
        downloadPath: values.downloadPath || './downloads',
        fileTypes: values.fileTypes || [],
        filter: values.filter,
        progress: 0,
        totalFiles: 0,
        completedFiles: 0,
        failedFiles: 0,
      };

      await createTask(taskData);
      message.success('下载任务创建成功！');
      form.resetFields();
    } catch (error) {
      message.error('创建任务失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTaskAction = async (action: string, taskId: string) => {
    try {
      switch (action) {
        case 'start':
          await startTask(taskId);
          message.success('任务已开始');
          break;
        case 'pause':
          await pauseTask(taskId);
          message.success('任务已暂停');
          break;
        case 'stop':
          await stopTask(taskId);
          message.success('任务已停止');
          break;
        case 'delete':
          await deleteTask(taskId);
          message.success('任务已删除');
          break;
      }
    } catch (error) {
      message.error(`操作失败: ${error}`);
    }
  };

  return (
    <div>
      <Title level={2}>
        <DownloadOutlined /> 下载管理
      </Title>

      <Row gutter={layoutConfig.content.gutter || [16, 16]}>
        {/* 创建下载任务 */}
        <Col {...getResponsiveGrid(24, 24, 8)}>
          <Card title="创建下载任务" size={isMobile ? 'small' : 'default'}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleCreateTask}
              onValuesChange={(changedValues) => {
                if ('accountId' in changedValues) {
                  setSelectedAccount(changedValues.accountId);
                }
              }}
              initialValues={{
                downloadPath: './downloads',
                fileTypes: ['image', 'video'],
              }}
            >
              <Form.Item
                name="accountId"
                label="选择账号"
                rules={[{ required: true, message: '请选择账号' }]}
              >
                <AccountSelector
                  accounts={accounts}
                  onChange={setSelectedAccount}
                  placeholder="选择要使用的Telegram账号"
                />
              </Form.Item>

              <Form.Item
                name="groupId"
                label="选择群组"
                rules={[{ required: true, message: '请选择群组' }]}
              >
                <GroupSelector
                  groups={groups}
                  placeholder="选择要下载的群组或频道"
                />
              </Form.Item>

              <Row gutter={8}>
                <Col span={isMobile ? 24 : 12}>
                  <Form.Item
                    name="startMessageId"
                    label="起始消息ID"
                    rules={[{ required: true, message: '请输入起始消息ID' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="1"
                      min={1}
                      size={isMobile ? 'large' : 'middle'}
                    />
                  </Form.Item>
                </Col>
                <Col span={isMobile ? 24 : 12}>
                  <Form.Item
                    name="endMessageId"
                    label="结束消息ID"
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="最新"
                      min={1}
                      size={isMobile ? 'large' : 'middle'}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="downloadPath"
                label="下载路径"
              >
                <Input placeholder="./downloads" />
              </Form.Item>

              <Form.Item
                name="fileTypes"
                label="文件类型"
              >
                <Checkbox.Group>
                  <Row>
                    <Col span={12}>
                      <Checkbox value="image">图片</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="video">视频</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="audio">音频</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="document">文档</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>

              <Form.Item
                name="filter"
                label="过滤条件"
              >
                <TextArea
                  rows={3}
                  placeholder="可选：输入关键词过滤消息内容"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<PlusOutlined />}
                  block
                >
                  创建下载任务
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 任务列表 */}
        <Col {...getResponsiveGrid(24, 24, 16)}>
          <Card title="下载任务列表" size={isMobile ? 'small' : 'default'}>
            <List
              loading={tasksLoading}
              dataSource={downloadTasks}
              renderItem={(task) => (
                <List.Item style={{ padding: '8px 0' }}>
                  <TaskCard
                    task={task}
                    onStart={(id) => handleTaskAction('start', id)}
                    onPause={(id) => handleTaskAction('pause', id)}
                    onStop={(id) => handleTaskAction('stop', id)}
                    onDelete={(id) => handleTaskAction('delete', id)}
                  />
                </List.Item>
              )}
              locale={{ emptyText: '暂无下载任务' }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Download;
