import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Select,
  InputNumber,
  Typography,
  Tabs,
  Space,
  message,
  Divider,
  List,
  Avatar,
  Tag,
  Modal,
  Row,
  Col,
} from 'antd';
import {
  SettingOutlined,
  UserOutlined,
  DownloadOutlined,
  GlobalOutlined,
  BellOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useSettingsStore } from '@/store/settingsStore';
import type {
  Account,
  SystemSettings,
  ProxySettings,
  NotificationSettings,
  TelegramSettings,
  FileSettings,
  PerformanceSettings,
  SecuritySettings,
  DatabaseSettings
} from '@/types';

const { Title, Text } = Typography;
const { Option } = Select;

const Settings: React.FC = () => {
  const [accountForm] = Form.useForm();
  const [settingsForm] = Form.useForm();
  const [proxyForm] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [telegramForm] = Form.useForm();
  const [fileForm] = Form.useForm();
  const [performanceForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [databaseForm] = Form.useForm();

  const [loading, setLoading] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [accountModalVisible, setAccountModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);

  const {
    settings,
    isLoading: settingsLoading,
    updateSettings,
    fetchSettings,
  } = useSettingsStore();

  useEffect(() => {
    fetchSettings();

    // 模拟账号数据
    setAccounts([
      {
        id: '1',
        name: '主账号',
        phone: '+86 138****1234',
        isActive: true,
        lastLogin: '2024-01-15 10:30:00',
      },
      {
        id: '2',
        name: '备用账号',
        phone: '+86 139****5678',
        isActive: false,
        lastLogin: '2024-01-14 15:20:00',
      },
    ]);
  }, [fetchSettings]);

  useEffect(() => {
    // 设置表单初始值
    settingsForm.setFieldsValue(settings);
    proxyForm.setFieldsValue(settings.proxySettings || {});
    notificationForm.setFieldsValue(settings.notificationSettings || {});
    telegramForm.setFieldsValue(settings.telegramSettings || {});
    fileForm.setFieldsValue(settings.fileSettings || {});
    performanceForm.setFieldsValue(settings.performanceSettings || {});
    securityForm.setFieldsValue(settings.securitySettings || {});
    databaseForm.setFieldsValue(settings.databaseSettings || {});
  }, [settings, settingsForm, proxyForm, notificationForm, telegramForm, fileForm, performanceForm, securityForm, databaseForm]);

  const handleUpdateSettings = async (values: Partial<SystemSettings>) => {
    setLoading(true);
    try {
      await updateSettings(values);
      message.success('设置更新成功');
    } catch (error) {
      message.error('设置更新失败');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProxy = async (values: ProxySettings) => {
    await handleUpdateSettings({ proxySettings: values });
  };

  const handleUpdateNotifications = async (values: NotificationSettings) => {
    await handleUpdateSettings({ notificationSettings: values });
  };

  const handleUpdateTelegram = async (values: TelegramSettings) => {
    await handleUpdateSettings({ telegramSettings: values });
  };

  const handleUpdateFile = async (values: FileSettings) => {
    await handleUpdateSettings({ fileSettings: values });
  };

  const handleUpdatePerformance = async (values: PerformanceSettings) => {
    await handleUpdateSettings({ performanceSettings: values });
  };

  const handleUpdateSecurity = async (values: SecuritySettings) => {
    await handleUpdateSettings({ securitySettings: values });
  };

  const handleUpdateDatabase = async (values: DatabaseSettings) => {
    await handleUpdateSettings({ databaseSettings: values });
  };

  const handleAddAccount = () => {
    setEditingAccount(null);
    accountForm.resetFields();
    setAccountModalVisible(true);
  };

  const handleEditAccount = (account: Account) => {
    setEditingAccount(account);
    accountForm.setFieldsValue(account);
    setAccountModalVisible(true);
  };

  const handleDeleteAccount = (accountId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个账号吗？此操作不可恢复。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        setAccounts(prev => prev.filter(acc => acc.id !== accountId));
        message.success('账号删除成功');
      },
    });
  };

  const handleSaveAccount = async (values: any) => {
    try {
      if (editingAccount) {
        // 更新账号
        setAccounts(prev => prev.map(acc =>
          acc.id === editingAccount.id ? { ...acc, ...values } : acc
        ));
        message.success('账号更新成功');
      } else {
        // 添加新账号
        const newAccount: Account = {
          id: Date.now().toString(),
          name: values.name,
          phone: values.phone,
          isActive: false,
          lastLogin: null,
        };
        setAccounts(prev => [...prev, newAccount]);
        message.success('账号添加成功');
      }
      setAccountModalVisible(false);
      accountForm.resetFields();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const tabItems = [
    {
      key: 'accounts',
      label: (
        <Space>
          <UserOutlined />
          账号管理
        </Space>
      ),
      children: (
        <Card>
          <div style={{ marginBottom: 16 }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddAccount}>
              添加账号
            </Button>
          </div>
          <List
            dataSource={accounts}
            renderItem={(account) => (
              <List.Item
                actions={[
                  <Button
                    key="edit"
                    type="text"
                    icon={<EditOutlined />}
                    onClick={() => handleEditAccount(account)}
                  >
                    编辑
                  </Button>,
                  <Button
                    key="delete"
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleDeleteAccount(account.id)}
                  >
                    删除
                  </Button>,
                ]}
              >
                <List.Item.Meta
                  avatar={<Avatar icon={<UserOutlined />} />}
                  title={
                    <Space>
                      <Text strong>{account.name}</Text>
                      <Tag color={account.isActive ? 'green' : 'default'}>
                        {account.isActive ? '在线' : '离线'}
                      </Tag>
                    </Space>
                  }
                  description={
                    <div>
                      <div>手机号: {account.phone}</div>
                      {account.lastLogin && (
                        <div>最后登录: {account.lastLogin}</div>
                      )}
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      ),
    },
    {
      key: 'download',
      label: (
        <Space>
          <DownloadOutlined />
          下载设置
        </Space>
      ),
      children: (
        <Card>
          <Form
            form={settingsForm}
            layout="vertical"
            onFinish={handleUpdateSettings}
          >
            <Form.Item
              name="downloadPath"
              label="默认下载路径"
              rules={[{ required: true, message: '请输入下载路径' }]}
            >
              <Input placeholder="./downloads" />
            </Form.Item>

            <Form.Item
              name="maxConcurrentTasks"
              label="最大并发任务数"
              rules={[{ required: true, message: '请输入最大并发任务数' }]}
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="theme"
              label="界面主题"
            >
              <Select>
                <Option value="light">亮色主题</Option>
                <Option value="dark">暗色主题</Option>
                <Option value="auto">跟随系统</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="language"
              label="界面语言"
            >
              <Select>
                <Option value="zh-CN">简体中文</Option>
                <Option value="en-US">English</Option>
              </Select>
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存设置
              </Button>
            </Form.Item>
          </Form>
        </Card>
      ),
    },
    {
      key: 'telegram',
      label: (
        <Space>
          <SettingOutlined />
          Telegram设置
        </Space>
      ),
      children: (
        <Card>
          <Form
            form={telegramForm}
            layout="vertical"
            onFinish={handleUpdateTelegram}
          >
            <Form.Item
              name="apiId"
              label="API ID"
              rules={[{ required: true, message: '请输入API ID' }]}
            >
              <InputNumber style={{ width: '100%' }} placeholder="从 my.telegram.org 获取" />
            </Form.Item>

            <Form.Item
              name="apiHash"
              label="API Hash"
              rules={[{ required: true, message: '请输入API Hash' }]}
            >
              <Input placeholder="从 my.telegram.org 获取" />
            </Form.Item>

            <Form.Item
              name="sessionName"
              label="会话名称"
            >
              <Input placeholder="telegram_session" />
            </Form.Item>

            <Form.Item
              name="botToken"
              label="Bot Token (可选)"
            >
              <Input placeholder="从 @BotFather 获取" />
            </Form.Item>

            <Form.Item
              name="sessionPath"
              label="会话文件路径"
            >
              <Input placeholder="./sessions" />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存Telegram设置
              </Button>
            </Form.Item>
          </Form>
        </Card>
      ),
    },
    {
      key: 'file',
      label: (
        <Space>
          <DownloadOutlined />
          文件设置
        </Space>
      ),
      children: (
        <Card>
          <Form
            form={fileForm}
            layout="vertical"
            onFinish={handleUpdateFile}
          >
            <Form.Item
              name="maxFileSize"
              label="最大文件大小 (MB)"
            >
              <InputNumber
                min={1}
                max={10240}
                style={{ width: '100%' }}
                formatter={value => `${value} MB`}
                parser={value => value?.replace(' MB', '') || ''}
              />
            </Form.Item>

            <Form.Item
              name="chunkSize"
              label="分块大小 (KB)"
            >
              <InputNumber
                min={64}
                max={10240}
                style={{ width: '100%' }}
                formatter={value => `${value} KB`}
                parser={value => value?.replace(' KB', '') || ''}
              />
            </Form.Item>

            <Form.Item
              name="mediaTypes"
              label="支持的媒体类型"
            >
              <Select
                mode="multiple"
                placeholder="选择媒体类型"
                options={[
                  { label: '音频', value: 'audio' },
                  { label: '文档', value: 'document' },
                  { label: '图片', value: 'photo' },
                  { label: '视频', value: 'video' },
                  { label: '语音', value: 'voice' },
                  { label: '动画', value: 'animation' },
                ]}
              />
            </Form.Item>

            <Form.Item name="hideFileName" valuePropName="checked">
              <Switch checkedChildren="隐藏文件名" unCheckedChildren="显示文件名" />
            </Form.Item>

            <Form.Item name="enableDownloadTxt" valuePropName="checked">
              <Switch checkedChildren="启用文本下载" unCheckedChildren="禁用文本下载" />
            </Form.Item>

            <Form.Item name="dropNoAudioVideo" valuePropName="checked">
              <Switch checkedChildren="丢弃无音视频文件" unCheckedChildren="保留所有文件" />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存文件设置
              </Button>
            </Form.Item>
          </Form>
        </Card>
      ),
    },
    {
      key: 'proxy',
      label: (
        <Space>
          <GlobalOutlined />
          代理设置
        </Space>
      ),
      children: (
        <Card>
          <Form
            form={proxyForm}
            layout="vertical"
            onFinish={handleUpdateProxy}
          >
            <Form.Item name="enabled" valuePropName="checked">
              <Switch checkedChildren="启用代理" unCheckedChildren="禁用代理" />
            </Form.Item>

            <Form.Item
              name="type"
              label="代理类型"
            >
              <Select>
                <Option value="http">HTTP</Option>
                <Option value="socks5">SOCKS5</Option>
              </Select>
            </Form.Item>

            <Row gutter={16}>
              <Col span={16}>
                <Form.Item
                  name="host"
                  label="代理服务器"
                  rules={[{ required: true, message: '请输入代理服务器地址' }]}
                >
                  <Input placeholder="127.0.0.1" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="port"
                  label="端口"
                  rules={[{ required: true, message: '请输入端口号' }]}
                >
                  <InputNumber min={1} max={65535} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="username" label="用户名">
                  <Input placeholder="可选" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="password" label="密码">
                  <Input.Password placeholder="可选" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存代理设置
              </Button>
            </Form.Item>
          </Form>
        </Card>
      ),
    },
    {
      key: 'notifications',
      label: (
        <Space>
          <BellOutlined />
          通知设置
        </Space>
      ),
      children: (
        <Card>
          <Form
            form={notificationForm}
            layout="vertical"
            onFinish={handleUpdateNotifications}
          >
            <Form.Item name="enabled" valuePropName="checked">
              <Switch checkedChildren="启用通知" unCheckedChildren="禁用通知" />
            </Form.Item>

            <Divider />

            <Form.Item name="taskCompletion" valuePropName="checked">
              <Switch checkedChildren="任务完成通知" unCheckedChildren="任务完成通知" />
            </Form.Item>

            <Form.Item name="taskFailure" valuePropName="checked">
              <Switch checkedChildren="任务失败通知" unCheckedChildren="任务失败通知" />
            </Form.Item>

            <Form.Item name="systemAlerts" valuePropName="checked">
              <Switch checkedChildren="系统警告通知" unCheckedChildren="系统警告通知" />
            </Form.Item>

            <Divider>邮件通知设置</Divider>

            <Form.Item name="emailEnabled" valuePropName="checked">
              <Switch checkedChildren="启用邮件通知" unCheckedChildren="禁用邮件通知" />
            </Form.Item>

            <Form.Item
              name="emailSmtpServer"
              label="SMTP服务器"
            >
              <Input placeholder="smtp.gmail.com" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={16}>
                <Form.Item
                  name="emailUsername"
                  label="邮件用户名"
                >
                  <Input placeholder="<EMAIL>" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="emailSmtpPort"
                  label="SMTP端口"
                >
                  <InputNumber min={1} max={65535} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="emailPassword"
              label="邮件密码"
            >
              <Input.Password placeholder="邮件密码或应用专用密码" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="emailFrom"
                  label="发件人邮箱"
                >
                  <Input placeholder="<EMAIL>" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="emailTo"
                  label="收件人邮箱"
                >
                  <Select
                    mode="tags"
                    placeholder="输入收件人邮箱地址"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存通知设置
              </Button>
            </Form.Item>
          </Form>
        </Card>
      ),
    },
    {
      key: 'performance',
      label: (
        <Space>
          <SettingOutlined />
          性能设置
        </Space>
      ),
      children: (
        <Card>
          <Form
            form={performanceForm}
            layout="vertical"
            onFinish={handleUpdatePerformance}
          >
            <Form.Item
              name="timeout"
              label="超时时间 (秒)"
            >
              <InputNumber min={5} max={300} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="retryAttempts"
              label="重试次数"
            >
              <InputNumber min={0} max={10} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="retryDelay"
              label="重试延迟 (秒)"
            >
              <InputNumber min={1} max={60} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="maxDownloadSpeed"
              label="最大下载速度 (KB/s, 0=无限制)"
            >
              <InputNumber min={0} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="maxConcurrentTransmissions"
              label="最大并发传输数"
            >
              <InputNumber min={1} max={10} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="startTimeout"
              label="启动超时 (秒)"
            >
              <InputNumber min={10} max={300} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存性能设置
              </Button>
            </Form.Item>
          </Form>
        </Card>
      ),
    },
    {
      key: 'security',
      label: (
        <Space>
          <SettingOutlined />
          安全设置
        </Space>
      ),
      children: (
        <Card>
          <Form
            form={securityForm}
            layout="vertical"
            onFinish={handleUpdateSecurity}
          >
            <Form.Item
              name="sessionTimeout"
              label="会话超时 (秒)"
            >
              <InputNumber min={300} max={86400} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="maxLoginAttempts"
              label="最大登录尝试次数"
            >
              <InputNumber min={1} max={20} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="loginAttemptTimeout"
              label="登录尝试超时 (秒)"
            >
              <InputNumber min={60} max={3600} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="webLoginSecret"
              label="Web登录密钥"
            >
              <Input.Password placeholder="设置Web登录密钥" />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存安全设置
              </Button>
            </Form.Item>
          </Form>
        </Card>
      ),
    },
    {
      key: 'database',
      label: (
        <Space>
          <SettingOutlined />
          数据库设置
        </Space>
      ),
      children: (
        <Card>
          <Form
            form={databaseForm}
            layout="vertical"
            onFinish={handleUpdateDatabase}
          >
            <Form.Item
              name="url"
              label="数据库URL"
              rules={[{ required: true, message: '请输入数据库URL' }]}
            >
              <Input placeholder="sqlite:///app.db" />
            </Form.Item>

            <Form.Item name="echo" valuePropName="checked">
              <Switch checkedChildren="启用SQL回显" unCheckedChildren="禁用SQL回显" />
            </Form.Item>

            <Form.Item
              name="poolSize"
              label="连接池大小"
            >
              <InputNumber min={1} max={50} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="maxOverflow"
              label="最大溢出连接数"
            >
              <InputNumber min={0} max={100} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存数据库设置
              </Button>
            </Form.Item>
          </Form>
        </Card>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>
        <SettingOutlined /> 系统设置
      </Title>

      <Tabs items={tabItems} />

      {/* 账号编辑模态框 */}
      <Modal
        title={editingAccount ? '编辑账号' : '添加账号'}
        open={accountModalVisible}
        onCancel={() => setAccountModalVisible(false)}
        footer={null}
      >
        <Form
          form={accountForm}
          layout="vertical"
          onFinish={handleSaveAccount}
        >
          <Form.Item
            name="name"
            label="账号名称"
            rules={[{ required: true, message: '请输入账号名称' }]}
          >
            <Input placeholder="输入账号名称" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="手机号"
            rules={[{ required: true, message: '请输入手机号' }]}
          >
            <Input placeholder="+86 138****1234" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingAccount ? '更新' : '添加'}
              </Button>
              <Button onClick={() => setAccountModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Settings;
